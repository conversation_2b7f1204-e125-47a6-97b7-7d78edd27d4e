/**
 * نظام قوائم الفحص التفاعلية للصيانة وإدارة المخاطر
 * Interactive Checklists System for Maintenance and Risk Management
 */

class InteractiveChecklists {
    constructor() {
        this.checklists = {
            maintenance: {
                title: 'قائمة فحص الصيانة الوقائية',
                description: 'قائمة شاملة للصيانة الوقائية للأجهزة الطبية',
                icon: 'fas fa-tools',
                items: [
                    {
                        id: 'visual_inspection',
                        text: 'فحص بصري للحالة العامة والتوصيلات',
                        category: 'فحص عام',
                        critical: true
                    },
                    {
                        id: 'power_cord',
                        text: 'فحص كابل الطاقة والمقابس',
                        category: 'فحص عام',
                        critical: true
                    },
                    {
                        id: 'electrical_safety',
                        text: 'اختبار السلامة الكهربائية (تسرب التيار)',
                        category: 'اختبارات السلامة',
                        critical: true
                    },
                    {
                        id: 'grounding',
                        text: 'اختبار مقاومة التأريض الوقائي',
                        category: 'اختبارات السلامة',
                        critical: true
                    },
                    {
                        id: 'insulation',
                        text: 'اختبار مقاومة العزل',
                        category: 'اختبارات السلامة',
                        critical: false
                    },
                    {
                        id: 'calibration',
                        text: 'معايرة أجهزة الاستشعار والقياس',
                        category: 'الأداء الوظيفي',
                        critical: true
                    },
                    {
                        id: 'alarms',
                        text: 'اختبار أنظمة الإنذار والتنبيه',
                        category: 'الأداء الوظيفي',
                        critical: true
                    },
                    {
                        id: 'display',
                        text: 'فحص الشاشة ووضوح العرض',
                        category: 'الأداء الوظيفي',
                        critical: false
                    },
                    {
                        id: 'cleaning',
                        text: 'تنظيف الأسطح الخارجية والداخلية',
                        category: 'النظافة والتطهير',
                        critical: false
                    },
                    {
                        id: 'disinfection',
                        text: 'تطهير الأجزاء الملامسة للمريض',
                        category: 'النظافة والتطهير',
                        critical: true
                    },
                    {
                        id: 'documentation',
                        text: 'توثيق نتائج الفحص والصيانة',
                        category: 'التوثيق',
                        critical: true
                    },
                    {
                        id: 'next_maintenance',
                        text: 'تحديد موعد الصيانة القادمة',
                        category: 'التوثيق',
                        critical: true
                    }
                ]
            },
            risk_assessment: {
                title: 'قائمة فحص تقييم المخاطر',
                description: 'قائمة منهجية لتقييم وإدارة مخاطر الأجهزة الطبية',
                icon: 'fas fa-shield-alt',
                items: [
                    {
                        id: 'hazard_identification',
                        text: 'تحديد جميع المخاطر المحتملة',
                        category: 'تحليل المخاطر',
                        critical: true
                    },
                    {
                        id: 'risk_estimation',
                        text: 'تقدير احتمالية وشدة كل خطر',
                        category: 'تحليل المخاطر',
                        critical: true
                    },
                    {
                        id: 'risk_evaluation',
                        text: 'تقييم مقبولية مستوى المخاطر',
                        category: 'تحليل المخاطر',
                        critical: true
                    },
                    {
                        id: 'control_measures',
                        text: 'تحديد إجراءات التحكم المطلوبة',
                        category: 'التحكم في المخاطر',
                        critical: true
                    },
                    {
                        id: 'implementation',
                        text: 'تطبيق إجراءات التحكم',
                        category: 'التحكم في المخاطر',
                        critical: true
                    },
                    {
                        id: 'effectiveness',
                        text: 'تقييم فعالية إجراءات التحكم',
                        category: 'التحكم في المخاطر',
                        critical: true
                    },
                    {
                        id: 'residual_risk',
                        text: 'تقييم المخاطر المتبقية',
                        category: 'المراجعة والمتابعة',
                        critical: true
                    },
                    {
                        id: 'acceptability',
                        text: 'تحديد مقبولية المخاطر المتبقية',
                        category: 'المراجعة والمتابعة',
                        critical: true
                    },
                    {
                        id: 'monitoring',
                        text: 'وضع خطة للمراقبة المستمرة',
                        category: 'المراجعة والمتابعة',
                        critical: false
                    },
                    {
                        id: 'review_schedule',
                        text: 'تحديد جدول المراجعة الدورية',
                        category: 'المراجعة والمتابعة',
                        critical: false
                    }
                ]
            },
            cybersecurity: {
                title: 'قائمة فحص الأمن السيبراني',
                description: 'قائمة للتحقق من الأمان السيبراني للأجهزة الطبية المتصلة',
                icon: 'fas fa-lock',
                items: [
                    {
                        id: 'network_security',
                        text: 'فحص أمان الشبكة والاتصالات',
                        category: 'أمان الشبكة',
                        critical: true
                    },
                    {
                        id: 'access_control',
                        text: 'مراجعة ضوابط الوصول والمصادقة',
                        category: 'أمان الشبكة',
                        critical: true
                    },
                    {
                        id: 'encryption',
                        text: 'التحقق من تشفير البيانات',
                        category: 'أمان البيانات',
                        critical: true
                    },
                    {
                        id: 'software_updates',
                        text: 'فحص تحديثات البرمجيات الأمنية',
                        category: 'إدارة النظام',
                        critical: true
                    },
                    {
                        id: 'vulnerability_scan',
                        text: 'فحص الثغرات الأمنية',
                        category: 'إدارة النظام',
                        critical: true
                    },
                    {
                        id: 'backup_integrity',
                        text: 'التحقق من سلامة النسخ الاحتياطية',
                        category: 'إدارة البيانات',
                        critical: false
                    },
                    {
                        id: 'incident_response',
                        text: 'مراجعة خطة الاستجابة للحوادث',
                        category: 'الاستجابة للطوارئ',
                        critical: false
                    }
                ]
            }
        };
        
        this.init();
    }

    init() {
        this.createChecklistInterfaces();
        this.setupEventListeners();
    }

    createChecklistInterfaces() {
        // إضافة قوائم الفحص للفصول المناسبة
        this.addChecklistToSection('section-6-3', 'maintenance'); // الصيانة الوقائية
        this.addChecklistToSection('section-5-4', 'risk_assessment'); // إدارة المخاطر
        this.addChecklistToSection('section-7-4', 'cybersecurity'); // الأمن السيبراني
    }

    addChecklistToSection(sectionId, checklistType) {
        const section = document.querySelector(`#${sectionId}`);
        if (!section) return;

        const checklist = this.checklists[checklistType];
        const checklistDiv = document.createElement('div');
        checklistDiv.className = 'interactive-checklist-container';
        checklistDiv.innerHTML = this.createChecklistHTML(checklistType, checklist);
        
        section.appendChild(checklistDiv);
    }

    createChecklistHTML(checklistType, checklist) {
        const categories = [...new Set(checklist.items.map(item => item.category))];
        
        return `
            <div class="interactive-checklist" data-checklist="${checklistType}">
                <div class="checklist-header">
                    <h4>
                        <i class="${checklist.icon}"></i>
                        ${checklist.title}
                    </h4>
                    <p>${checklist.description}</p>
                    <div class="checklist-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                        <span class="progress-text">0 من ${checklist.items.length} مكتمل</span>
                    </div>
                </div>
                
                <div class="checklist-controls">
                    <button class="btn btn-secondary checklist-expand-btn">
                        <i class="fas fa-expand"></i>
                        توسيع الكل
                    </button>
                    <button class="btn btn-secondary checklist-reset-btn">
                        <i class="fas fa-undo"></i>
                        إعادة تعيين
                    </button>
                    <button class="btn btn-primary checklist-export-btn">
                        <i class="fas fa-download"></i>
                        تصدير التقرير
                    </button>
                </div>
                
                <div class="checklist-categories">
                    ${categories.map(category => this.createCategoryHTML(checklistType, category, checklist.items)).join('')}
                </div>
                
                <div class="checklist-summary" style="display: none;">
                    <h5>ملخص النتائج</h5>
                    <div class="summary-stats">
                        <div class="stat-item">
                            <span class="stat-number completed-count">0</span>
                            <span class="stat-label">مكتمل</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number pending-count">${checklist.items.length}</span>
                            <span class="stat-label">متبقي</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number critical-pending-count">${checklist.items.filter(item => item.critical).length}</span>
                            <span class="stat-label">حرج متبقي</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    createCategoryHTML(checklistType, category, items) {
        const categoryItems = items.filter(item => item.category === category);
        
        return `
            <div class="checklist-category">
                <div class="category-header">
                    <h5>
                        <i class="fas fa-folder"></i>
                        ${category}
                        <span class="category-progress">(0/${categoryItems.length})</span>
                    </h5>
                    <button class="category-toggle">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
                <div class="category-items">
                    ${categoryItems.map(item => this.createItemHTML(checklistType, item)).join('')}
                </div>
            </div>
        `;
    }

    createItemHTML(checklistType, item) {
        return `
            <div class="checklist-item ${item.critical ? 'critical' : ''}" data-item="${item.id}">
                <label class="item-checkbox">
                    <input type="checkbox" data-checklist="${checklistType}" data-item="${item.id}">
                    <span class="checkmark"></span>
                    <span class="item-text">${item.text}</span>
                    ${item.critical ? '<span class="critical-badge">حرج</span>' : ''}
                </label>
                <div class="item-actions">
                    <button class="item-note-btn" title="إضافة ملاحظة">
                        <i class="fas fa-sticky-note"></i>
                    </button>
                    <button class="item-photo-btn" title="إضافة صورة">
                        <i class="fas fa-camera"></i>
                    </button>
                </div>
                <div class="item-note" style="display: none;">
                    <textarea placeholder="أضف ملاحظة..." rows="2"></textarea>
                    <button class="btn btn-sm btn-primary save-note-btn">حفظ</button>
                </div>
            </div>
        `;
    }

    setupEventListeners() {
        document.addEventListener('change', (e) => {
            if (e.target.type === 'checkbox' && e.target.dataset.checklist) {
                this.updateProgress(e.target.dataset.checklist);
            }
        });

        document.addEventListener('click', (e) => {
            if (e.target.closest('.category-toggle')) {
                this.toggleCategory(e.target.closest('.checklist-category'));
            }
            
            if (e.target.closest('.item-note-btn')) {
                this.toggleItemNote(e.target.closest('.checklist-item'));
            }
            
            if (e.target.closest('.save-note-btn')) {
                this.saveItemNote(e.target.closest('.checklist-item'));
            }
            
            if (e.target.closest('.checklist-reset-btn')) {
                const checklist = e.target.closest('.interactive-checklist');
                this.resetChecklist(checklist.dataset.checklist);
            }
            
            if (e.target.closest('.checklist-export-btn')) {
                const checklist = e.target.closest('.interactive-checklist');
                this.exportReport(checklist.dataset.checklist);
            }
            
            if (e.target.closest('.checklist-expand-btn')) {
                this.toggleAllCategories(e.target.closest('.interactive-checklist'));
            }
        });
    }

    updateProgress(checklistType) {
        const container = document.querySelector(`[data-checklist="${checklistType}"]`);
        if (!container) return;

        const checkboxes = container.querySelectorAll('input[type="checkbox"]');
        const checkedBoxes = container.querySelectorAll('input[type="checkbox"]:checked');
        const criticalBoxes = container.querySelectorAll('.critical input[type="checkbox"]');
        const checkedCritical = container.querySelectorAll('.critical input[type="checkbox"]:checked');
        
        const progress = (checkedBoxes.length / checkboxes.length) * 100;
        
        // تحديث شريط التقدم
        const progressFill = container.querySelector('.progress-fill');
        const progressText = container.querySelector('.progress-text');
        
        progressFill.style.width = `${progress}%`;
        progressText.textContent = `${checkedBoxes.length} من ${checkboxes.length} مكتمل`;
        
        // تحديث إحصائيات الملخص
        const summary = container.querySelector('.checklist-summary');
        if (checkedBoxes.length > 0) {
            summary.style.display = 'block';
            summary.querySelector('.completed-count').textContent = checkedBoxes.length;
            summary.querySelector('.pending-count').textContent = checkboxes.length - checkedBoxes.length;
            summary.querySelector('.critical-pending-count').textContent = criticalBoxes.length - checkedCritical.length;
        } else {
            summary.style.display = 'none';
        }
        
        // تحديث تقدم الفئات
        this.updateCategoryProgress(container);
    }

    updateCategoryProgress(container) {
        const categories = container.querySelectorAll('.checklist-category');
        categories.forEach(category => {
            const checkboxes = category.querySelectorAll('input[type="checkbox"]');
            const checkedBoxes = category.querySelectorAll('input[type="checkbox"]:checked');
            const progressSpan = category.querySelector('.category-progress');
            
            progressSpan.textContent = `(${checkedBoxes.length}/${checkboxes.length})`;
            
            if (checkedBoxes.length === checkboxes.length && checkboxes.length > 0) {
                category.classList.add('completed');
            } else {
                category.classList.remove('completed');
            }
        });
    }

    toggleCategory(category) {
        const items = category.querySelector('.category-items');
        const toggle = category.querySelector('.category-toggle i');
        
        if (items.style.display === 'none') {
            items.style.display = 'block';
            toggle.className = 'fas fa-chevron-down';
        } else {
            items.style.display = 'none';
            toggle.className = 'fas fa-chevron-left';
        }
    }

    toggleAllCategories(container) {
        const categories = container.querySelectorAll('.checklist-category');
        const expandBtn = container.querySelector('.checklist-expand-btn');
        const allExpanded = Array.from(categories).every(cat => 
            cat.querySelector('.category-items').style.display !== 'none'
        );
        
        categories.forEach(category => {
            const items = category.querySelector('.category-items');
            const toggle = category.querySelector('.category-toggle i');
            
            if (allExpanded) {
                items.style.display = 'none';
                toggle.className = 'fas fa-chevron-left';
            } else {
                items.style.display = 'block';
                toggle.className = 'fas fa-chevron-down';
            }
        });
        
        expandBtn.innerHTML = allExpanded ? 
            '<i class="fas fa-expand"></i> توسيع الكل' : 
            '<i class="fas fa-compress"></i> طي الكل';
    }

    toggleItemNote(item) {
        const noteDiv = item.querySelector('.item-note');
        noteDiv.style.display = noteDiv.style.display === 'none' ? 'block' : 'none';
    }

    saveItemNote(item) {
        const textarea = item.querySelector('textarea');
        const noteBtn = item.querySelector('.item-note-btn');
        
        if (textarea.value.trim()) {
            noteBtn.classList.add('has-note');
            noteBtn.title = `ملاحظة: ${textarea.value.substring(0, 50)}...`;
        } else {
            noteBtn.classList.remove('has-note');
            noteBtn.title = 'إضافة ملاحظة';
        }
        
        this.toggleItemNote(item);
    }

    resetChecklist(checklistType) {
        const container = document.querySelector(`[data-checklist="${checklistType}"]`);
        if (!container) return;
        
        // إعادة تعيين جميع الخانات
        container.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.checked = false;
        });
        
        // إعادة تعيين الملاحظات
        container.querySelectorAll('textarea').forEach(textarea => {
            textarea.value = '';
        });
        
        container.querySelectorAll('.item-note-btn').forEach(btn => {
            btn.classList.remove('has-note');
            btn.title = 'إضافة ملاحظة';
        });
        
        // تحديث التقدم
        this.updateProgress(checklistType);
    }

    exportReport(checklistType) {
        const container = document.querySelector(`[data-checklist="${checklistType}"]`);
        const checklist = this.checklists[checklistType];
        
        if (!container || !checklist) return;
        
        const checkboxes = container.querySelectorAll('input[type="checkbox"]');
        const checkedBoxes = container.querySelectorAll('input[type="checkbox"]:checked');
        
        let report = `تقرير ${checklist.title}\n`;
        report += `التاريخ: ${new Date().toLocaleDateString('ar-SA')}\n`;
        report += `التقدم: ${checkedBoxes.length}/${checkboxes.length} (${Math.round((checkedBoxes.length/checkboxes.length)*100)}%)\n\n`;
        
        // تجميع النتائج حسب الفئة
        const categories = [...new Set(checklist.items.map(item => item.category))];
        categories.forEach(category => {
            report += `\n${category}:\n`;
            report += '='.repeat(category.length + 1) + '\n';
            
            checklist.items.filter(item => item.category === category).forEach(item => {
                const checkbox = container.querySelector(`input[data-item="${item.id}"]`);
                const isChecked = checkbox ? checkbox.checked : false;
                const note = container.querySelector(`input[data-item="${item.id}"]`)?.closest('.checklist-item')?.querySelector('textarea')?.value || '';
                
                report += `${isChecked ? '✓' : '✗'} ${item.text}`;
                if (item.critical) report += ' (حرج)';
                if (note) report += `\n   ملاحظة: ${note}`;
                report += '\n';
            });
        });
        
        // تنزيل التقرير
        const blob = new Blob([report], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${checklist.title.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
}

// تهيئة قوائم الفحص التفاعلية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    new InteractiveChecklists();
});
