/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Colors */
    --primary-color: #2c5aa0;
    --secondary-color: #1e3a5f;
    --accent-color: #4a90e2;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-color: #f8f9fa;
    --dark-color: #2c3e50;
    --text-primary: #2c3e50;
    --text-secondary: #7f8c8d;
    --border-color: #e9ecef;
    --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 20px rgba(0,0,0,0.15);
    --shadow-heavy: 0 8px 30px rgba(0,0,0,0.2);
    
    /* Typography */
    --font-family: 'Noto Sans Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: #ffffff;
    direction: rtl;
    text-align: right;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--accent-color);
}

/* Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.section-title {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
    position: relative;
    color: var(--primary-color);
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    right: 50%;
    transform: translateX(50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: var(--radius-sm);
}

/* Header and Navigation */
.main-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-light);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    transition: var(--transition-normal);
}

.navbar {
    padding: var(--spacing-md) 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
}

.nav-logo i {
    font-size: var(--font-size-2xl);
}

.nav-menu {
    display: flex;
    gap: var(--spacing-xl);
    align-items: center;
}

.nav-link {
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    background-color: var(--primary-color);
    color: white;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.nav-toggle .bar {
    width: 25px;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 120px 0 var(--spacing-3xl);
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
}

.hero-content {
    animation: fadeInUp 1s ease;
}

.hero-title {
    margin-bottom: var(--spacing-lg);
}

.title-main {
    display: block;
    font-size: var(--font-size-5xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.title-sub {
    display: block;
    font-size: var(--font-size-3xl);
    font-weight: 500;
    color: var(--secondary-color);
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: 1.8;
}

.hero-author {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.8);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-light);
}

.hero-author i {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
}

.author-affiliation {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-lg);
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition-normal);
    border: none;
    cursor: pointer;
    font-size: var(--font-size-base);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    box-shadow: var(--shadow-medium);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
    color: white;
}

.btn-secondary {
    background: white;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    box-shadow: var(--shadow-light);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.medical-illustration {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    padding: var(--spacing-2xl);
}

.medical-illustration i {
    font-size: 4rem;
    color: var(--primary-color);
    opacity: 0.8;
    transition: var(--transition-normal);
    animation: float 3s ease-in-out infinite;
}

.medical-illustration i:nth-child(2) {
    animation-delay: 0.5s;
}

.medical-illustration i:nth-child(3) {
    animation-delay: 1s;
}

.medical-illustration i:nth-child(4) {
    animation-delay: 1.5s;
}

.medical-illustration i:hover {
    transform: scale(1.1);
    color: var(--accent-color);
}

/* About Section */
.about {
    padding: var(--spacing-3xl) 0;
    background: white;
}

.about-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.about-card {
    text-align: center;
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    background: white;
    box-shadow: var(--shadow-light);
    transition: var(--transition-normal);
    border: 1px solid var(--border-color);
}

.about-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.card-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-lg);
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.card-icon i {
    font-size: var(--font-size-2xl);
    color: white;
}

.about-card h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

/* Contents Overview */
.contents-overview {
    padding: var(--spacing-3xl) 0;
    background: var(--light-color);
}

.parts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
}

.part-card {
    background: white;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-medium);
    transition: var(--transition-normal);
    border: 1px solid var(--border-color);
}

.part-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
}

.part-header {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    padding: var(--spacing-xl);
    text-align: center;
}

.part-number {
    display: block;
    font-size: var(--font-size-sm);
    opacity: 0.9;
    margin-bottom: var(--spacing-sm);
}

.part-header h3 {
    margin: 0;
    color: white;
}

.part-content {
    padding: var(--spacing-xl);
}

.chapter-list {
    list-style: none;
    margin-bottom: var(--spacing-lg);
}

.chapter-list li {
    margin-bottom: var(--spacing-sm);
}

.chapter-list a {
    display: block;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.chapter-list a:hover {
    background: var(--light-color);
    color: var(--primary-color);
}

.part-link {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    background: var(--primary-color);
    color: white;
    font-weight: 600;
    transition: var(--transition-fast);
}

.part-link:hover {
    background: var(--secondary-color);
    color: white;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Author Section */
.author {
    padding: var(--spacing-3xl) 0;
    background: white;
}

.author-card {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    box-shadow: var(--shadow-medium);
}

.author-info h3 {
    color: var(--primary-color);
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-sm);
}

.author-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: var(--spacing-xs);
}

.author-institution {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
}

.author-description {
    font-size: var(--font-size-base);
    line-height: 1.8;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Quick Access */
.quick-access {
    padding: var(--spacing-3xl) 0;
    background: var(--light-color);
}

.quick-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    max-width: 800px;
    margin: 0 auto;
}

.quick-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xl);
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-light);
    transition: var(--transition-normal);
    text-decoration: none;
    color: var(--text-primary);
}

.quick-link:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
    color: var(--primary-color);
}

.quick-link i {
    font-size: var(--font-size-2xl);
    color: var(--primary-color);
}

.quick-link span {
    font-weight: 600;
}

/* Footer */
.main-footer {
    background: var(--dark-color);
    color: white;
    padding: var(--spacing-3xl) 0 var(--spacing-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.footer-section h4 {
    color: white;
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-lg);
}

.footer-section p {
    color: #bdc3c7;
    margin-bottom: var(--spacing-sm);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: var(--spacing-sm);
}

.footer-section ul li a {
    color: #bdc3c7;
    transition: var(--transition-fast);
}

.footer-section ul li a:hover {
    color: white;
}

.footer-bottom {
    text-align: center;
    padding-top: var(--spacing-lg);
    border-top: 1px solid #34495e;
    color: #bdc3c7;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 100%;
        right: 0;
        width: 100%;
        background: white;
        flex-direction: column;
        padding: var(--spacing-lg);
        box-shadow: var(--shadow-medium);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: var(--transition-normal);
    }

    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--spacing-xl);
    }

    .title-main {
        font-size: var(--font-size-3xl);
    }

    .title-sub {
        font-size: var(--font-size-xl);
    }

    .hero-subtitle {
        font-size: var(--font-size-lg);
    }

    .hero-buttons {
        justify-content: center;
    }

    .about-grid {
        grid-template-columns: 1fr;
    }

    .parts-grid {
        grid-template-columns: 1fr;
    }

    .quick-links {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--spacing-md);
    }

    .hero {
        padding: 100px 0 var(--spacing-2xl);
    }

    .title-main {
        font-size: var(--font-size-2xl);
    }

    .title-sub {
        font-size: var(--font-size-lg);
    }

    .btn {
        padding: var(--spacing-sm) var(--spacing-lg);
        font-size: var(--font-size-sm);
    }

    .quick-links {
        grid-template-columns: 1fr;
    }

    .parts-grid {
        gap: var(--spacing-lg);
    }

    .part-card {
        margin: 0 var(--spacing-sm);
    }
}

/* Print Styles */
@media print {
    .main-header,
    .nav-toggle,
    .hero-buttons,
    .quick-access,
    .main-footer {
        display: none;
    }

    .hero {
        padding: var(--spacing-lg) 0;
        background: white;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
    }

    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
    }

    .part-card {
        page-break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #000080;
        --secondary-color: #000000;
        --text-primary: #000000;
        --text-secondary: #333333;
        --border-color: #000000;
    }

    .btn-primary {
        background: #000080;
        border: 2px solid #000080;
    }

    .btn-secondary {
        background: white;
        color: #000080;
        border: 2px solid #000080;
    }
}

/* Focus styles for accessibility */
a:focus,
button:focus,
.btn:focus {
    outline: 3px solid var(--accent-color);
    outline-offset: 2px;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Selection styles */
::selection {
    background: var(--primary-color);
    color: white;
}

/* Search Overlay Styles */
.search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    z-index: 2000;
    display: none;
    align-items: flex-start;
    justify-content: center;
    padding-top: 10vh;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.search-overlay.active {
    opacity: 1;
}

.search-container {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-heavy);
    width: 90%;
    max-width: 600px;
    max-height: 70vh;
    overflow: hidden;
    transform: translateY(-20px);
    transition: transform 0.3s ease;
}

.search-overlay.active .search-container {
    transform: translateY(0);
}

.search-header {
    display: flex;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.search-header input {
    flex: 1;
    border: none;
    outline: none;
    font-size: var(--font-size-lg);
    font-family: var(--font-family);
    padding: var(--spacing-sm);
    background: transparent;
    color: var(--text-primary);
    direction: rtl;
    text-align: right;
}

.search-header input::placeholder {
    color: var(--text-secondary);
}

.search-close {
    background: none;
    border: none;
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
    margin-right: var(--spacing-md);
}

.search-close:hover {
    background: var(--light-color);
    color: var(--text-primary);
}

.search-results {
    max-height: 50vh;
    overflow-y: auto;
    padding: var(--spacing-lg);
}

.search-placeholder,
.search-no-results {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-secondary);
}

.search-placeholder i,
.search-no-results i {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
    opacity: 0.5;
}

.search-result-item {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition-fast);
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-item:hover {
    background: var(--light-color);
}

.search-result-item h4 {
    margin-bottom: var(--spacing-sm);
}

.search-result-item h4 a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
}

.search-result-item h4 a:hover {
    color: var(--accent-color);
}

.search-result-item p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin: 0;
}

/* Keyboard navigation styles */
.keyboard-navigation *:focus {
    outline: 3px solid var(--accent-color);
    outline-offset: 2px;
}

.skip-link:focus {
    top: 6px !important;
}

/* Quiz System Styles */
.quiz-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
    color: white;
    text-align: center;
}

.quiz-intro h4 {
    margin-bottom: var(--spacing-sm);
    font-size: 1.3rem;
}

.quiz-intro p {
    margin-bottom: var(--spacing-md);
    opacity: 0.9;
}

.quiz-start-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.quiz-start-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

/* Quiz Modal Styles */
.quiz-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: var(--spacing-md);
}

.quiz-modal-content {
    background: white;
    border-radius: 12px;
    width: 100%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.quiz-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid #eee;
    background: var(--primary-color);
    color: white;
    border-radius: 12px 12px 0 0;
}

.quiz-title {
    margin: 0;
    font-size: 1.2rem;
}

.quiz-close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 4px;
    transition: background 0.3s ease;
}

.quiz-close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.quiz-progress {
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid #eee;
}

.quiz-progress-bar {
    width: 100%;
    height: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
}

.quiz-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transition: width 0.3s ease;
}

.quiz-progress-text {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.quiz-content {
    padding: var(--spacing-lg);
}

.quiz-question h4 {
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
    font-size: 1.1rem;
    line-height: 1.6;
}

.quiz-options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.quiz-option {
    display: flex;
    align-items: center;
    padding: var(--spacing-md);
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fafafa;
}

.quiz-option:hover {
    border-color: var(--primary-color);
    background: #f0f8ff;
}

.quiz-option input[type="radio"] {
    margin-left: var(--spacing-sm);
    transform: scale(1.2);
}

.quiz-option input[type="radio"]:checked + .quiz-option-text {
    color: var(--primary-color);
    font-weight: 600;
}

.quiz-option:has(input:checked) {
    border-color: var(--primary-color);
    background: #f0f8ff;
}

.quiz-navigation {
    display: flex;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-top: 1px solid #eee;
    background: #fafafa;
    border-radius: 0 0 12px 12px;
}

.quiz-navigation .btn {
    min-width: 120px;
}

/* Quiz Results Styles */
.quiz-results {
    text-align: center;
}

.quiz-score {
    padding: var(--spacing-xl);
    border-radius: 12px;
    margin-bottom: var(--spacing-lg);
}

.quiz-results.excellent .quiz-score {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
}

.quiz-results.good .quiz-score {
    background: linear-gradient(135deg, #FF9800, #f57c00);
    color: white;
}

.quiz-results.needs-improvement .quiz-score {
    background: linear-gradient(135deg, #f44336, #d32f2f);
    color: white;
}

.quiz-score i {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
}

.quiz-score h3 {
    margin: var(--spacing-md) 0;
    font-size: 1.5rem;
}

.quiz-percentage {
    font-size: 3rem;
    font-weight: bold;
    margin: var(--spacing-md) 0;
}

.quiz-message {
    font-size: 1.1rem;
    margin: 0;
    opacity: 0.9;
}

.quiz-review {
    text-align: right;
}

.quiz-review h4 {
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: var(--spacing-sm);
}

.quiz-review-item {
    background: #f9f9f9;
    border-radius: 8px;
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    border-right: 4px solid #ddd;
}

.quiz-review-item.correct {
    border-right-color: #4CAF50;
    background: #f1f8e9;
}

.quiz-review-item.incorrect {
    border-right-color: #f44336;
    background: #ffebee;
}

.quiz-review-question {
    margin-bottom: var(--spacing-sm);
    font-weight: 600;
}

.quiz-review-answer,
.quiz-review-correct {
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.quiz-review-label {
    font-weight: 600;
    color: var(--text-secondary);
    min-width: 100px;
}

.quiz-review-explanation {
    background: rgba(255, 255, 255, 0.7);
    padding: var(--spacing-sm);
    border-radius: 4px;
    font-size: 0.9rem;
    line-height: 1.5;
}

.text-success {
    color: #4CAF50;
}

.text-danger {
    color: #f44336;
}

/* Responsive Quiz Styles */
@media (max-width: 768px) {
    .quiz-modal {
        padding: var(--spacing-sm);
    }

    .quiz-modal-content {
        max-height: 95vh;
    }

    .quiz-header {
        padding: var(--spacing-md);
    }

    .quiz-title {
        font-size: 1rem;
    }

    .quiz-content {
        padding: var(--spacing-md);
    }

    .quiz-navigation {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .quiz-navigation .btn {
        width: 100%;
    }

    .quiz-percentage {
        font-size: 2rem;
    }

    .quiz-review-answer,
    .quiz-review-correct {
        flex-direction: column;
        align-items: flex-start;
    }

    .quiz-review-label {
        min-width: auto;
    }
}

/* Risk Calculator Styles */
.risk-calculator-container {
    margin: var(--spacing-xl) 0;
}

.risk-calculator {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    padding: var(--spacing-xl);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-medium);
}

.calculator-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.calculator-header h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
    font-size: 1.4rem;
}

.calculator-header p {
    color: var(--text-secondary);
    margin: 0;
}

.calculator-inputs {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.input-group {
    display: flex;
    flex-direction: column;
}

.input-group label {
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.calculator-select {
    padding: var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    background: white;
    transition: border-color 0.3s ease;
    direction: rtl;
}

.calculator-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
}

.calculator-btn {
    grid-column: 1 / -1;
    justify-self: center;
    min-width: 200px;
}

.calculator-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Risk Result Styles */
.risk-result {
    margin: var(--spacing-xl) 0;
}

.risk-result-content {
    background: white;
    border-radius: 12px;
    padding: var(--spacing-xl);
    border-right: 4px solid;
    box-shadow: var(--shadow-light);
}

.risk-score {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    border-radius: 8px;
    margin-bottom: var(--spacing-lg);
    color: white;
}

.score-number {
    font-size: 2.5rem;
    font-weight: bold;
}

.score-level {
    font-size: 1.2rem;
    font-weight: 600;
}

.risk-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.risk-calculation,
.risk-action,
.risk-recommendations {
    background: #f8f9fa;
    padding: var(--spacing-md);
    border-radius: 8px;
}

.risk-calculation h6,
.risk-action h6,
.risk-recommendations h6 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
    font-size: 1rem;
}

.risk-recommendations ul {
    margin: 0;
    padding-right: var(--spacing-lg);
}

.risk-recommendations li {
    margin-bottom: var(--spacing-xs);
}

/* Risk Matrix Styles */
.risk-matrix-display {
    margin: var(--spacing-xl) 0;
}

.risk-matrix-display h5 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
}

.matrix-table {
    overflow-x: auto;
}

.risk-matrix-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

.risk-matrix-table th,
.risk-matrix-table td {
    padding: var(--spacing-md);
    text-align: center;
    border: 1px solid #ddd;
    font-weight: 600;
}

.risk-matrix-table th {
    background: var(--primary-color);
    color: white;
    font-size: 0.9rem;
}

.severity-label {
    background: var(--light-color);
    color: var(--text-primary);
    font-weight: 600;
}

.risk-cell {
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.risk-cell:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    z-index: 10;
}

.risk-cell.highlighted {
    animation: pulse 1s infinite;
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.8);
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Examples Section */
.risk-examples {
    margin-top: var(--spacing-xl);
}

.risk-examples h5 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
}

.examples-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.example-card {
    background: white;
    padding: var(--spacing-lg);
    border-radius: 8px;
    text-align: center;
    box-shadow: var(--shadow-light);
    transition: transform 0.3s ease;
}

.example-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.example-card h6 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
    font-size: 1rem;
}

.example-card p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: var(--spacing-md);
}

.example-btn {
    font-size: 0.85rem;
    padding: var(--spacing-sm) var(--spacing-md);
}

/* Responsive Risk Calculator */
@media (max-width: 768px) {
    .calculator-inputs {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .risk-details {
        grid-template-columns: 1fr;
    }

    .examples-grid {
        grid-template-columns: 1fr;
    }

    .risk-matrix-table {
        font-size: 0.8rem;
    }

    .risk-matrix-table th,
    .risk-matrix-table td {
        padding: var(--spacing-sm);
    }

    .score-number {
        font-size: 2rem;
    }

    .score-level {
        font-size: 1rem;
    }
}

/* Interactive Checklists Styles */
.interactive-checklist-container {
    margin: var(--spacing-xl) 0;
}

.interactive-checklist {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    padding: var(--spacing-xl);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-medium);
}

.checklist-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.checklist-header h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
    font-size: 1.4rem;
}

.checklist-header p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
}

.checklist-progress {
    max-width: 400px;
    margin: 0 auto;
}

.progress-bar {
    width: 100%;
    height: 12px;
    background: #e0e0e0;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--success-color), #27ae60);
    transition: width 0.5s ease;
    border-radius: 6px;
}

.progress-text {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 600;
}

.checklist-controls {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
    flex-wrap: wrap;
}

.checklist-controls .btn {
    font-size: 0.9rem;
    padding: var(--spacing-sm) var(--spacing-md);
}

/* Category Styles */
.checklist-category {
    background: white;
    border-radius: 8px;
    margin-bottom: var(--spacing-md);
    box-shadow: var(--shadow-light);
    overflow: hidden;
    transition: all 0.3s ease;
}

.checklist-category.completed {
    border-right: 4px solid var(--success-color);
}

.category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    background: #f8f9fa;
    cursor: pointer;
    transition: background 0.3s ease;
}

.category-header:hover {
    background: #e9ecef;
}

.category-header h5 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.category-progress {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: normal;
}

.category-toggle {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.2rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 4px;
    transition: all 0.3s ease;
}

.category-toggle:hover {
    background: rgba(0, 0, 0, 0.1);
    color: var(--primary-color);
}

.category-items {
    padding: var(--spacing-md);
}

/* Checklist Item Styles */
.checklist-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: 6px;
    margin-bottom: var(--spacing-sm);
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.checklist-item:hover {
    background: #f0f8ff;
    border-color: var(--primary-color);
}

.checklist-item.critical {
    border-right: 3px solid var(--danger-color);
    background: #fff5f5;
}

.item-checkbox {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    cursor: pointer;
    flex: 1;
    font-size: 0.95rem;
    line-height: 1.5;
}

.item-checkbox input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    flex-shrink: 0;
    margin-top: 2px;
}

.item-checkbox input[type="checkbox"]:checked + .checkmark {
    background: var(--success-color);
    border-color: var(--success-color);
}

.item-checkbox input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    color: white;
    font-weight: bold;
    font-size: 14px;
}

.item-text {
    color: var(--text-primary);
    transition: color 0.3s ease;
}

.item-checkbox input[type="checkbox"]:checked + .checkmark + .item-text {
    color: var(--text-secondary);
    text-decoration: line-through;
}

.critical-badge {
    background: var(--danger-color);
    color: white;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 600;
    margin-right: var(--spacing-sm);
}

.item-actions {
    display: flex;
    gap: var(--spacing-xs);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.checklist-item:hover .item-actions {
    opacity: 1;
}

.item-note-btn,
.item-photo-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 0.9rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 4px;
    transition: all 0.3s ease;
}

.item-note-btn:hover,
.item-photo-btn:hover {
    background: var(--light-color);
    color: var(--primary-color);
}

.item-note-btn.has-note {
    color: var(--warning-color);
    background: rgba(243, 156, 18, 0.1);
}

.item-note {
    width: 100%;
    margin-top: var(--spacing-sm);
    padding: var(--spacing-sm);
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
}

.item-note textarea {
    width: 100%;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: var(--spacing-sm);
    font-family: var(--font-family);
    font-size: 0.9rem;
    resize: vertical;
    margin-bottom: var(--spacing-sm);
    direction: rtl;
}

.item-note textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.save-note-btn {
    font-size: 0.8rem;
    padding: var(--spacing-xs) var(--spacing-sm);
}

/* Summary Styles */
.checklist-summary {
    background: white;
    border-radius: 8px;
    padding: var(--spacing-lg);
    margin-top: var(--spacing-xl);
    box-shadow: var(--shadow-light);
}

.checklist-summary h5 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
    text-align: center;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 600;
}

/* Responsive Checklist Styles */
@media (max-width: 768px) {
    .checklist-controls {
        flex-direction: column;
        align-items: center;
    }

    .checklist-controls .btn {
        width: 100%;
        max-width: 200px;
    }

    .checklist-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .item-actions {
        opacity: 1;
        margin-top: var(--spacing-sm);
    }

    .summary-stats {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .category-header {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .category-header h5 {
        font-size: 1rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }
}

/* Glossary System Styles */
.glossary-toggle-btn {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50px;
    padding: var(--spacing-md) var(--spacing-lg);
    box-shadow: var(--shadow-medium);
    cursor: pointer;
    z-index: 999;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
}

.glossary-toggle-btn:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-large);
}

.glossary-toggle-btn i {
    font-size: 1.2rem;
}

/* Glossary Modal */
.glossary-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: var(--spacing-md);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.glossary-modal.active {
    opacity: 1;
}

.glossary-modal-content {
    background: white;
    border-radius: 12px;
    width: 100%;
    max-width: 900px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
}

.glossary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    background: var(--primary-color);
    color: white;
}

.glossary-header h3 {
    margin: 0;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.glossary-close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 4px;
    transition: background 0.3s ease;
}

.glossary-close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Search and Filters */
.glossary-search {
    padding: var(--spacing-lg);
    border-bottom: 1px solid #eee;
    background: #f8f9fa;
}

.search-input-container {
    position: relative;
    margin-bottom: var(--spacing-md);
}

.search-input-container i {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

#glossary-search {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-xl) var(--spacing-md) var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    direction: rtl;
    transition: border-color 0.3s ease;
}

#glossary-search:focus {
    outline: none;
    border-color: var(--primary-color);
}

.glossary-filters {
    display: flex;
    gap: var(--spacing-md);
}

#category-filter {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: 6px;
    font-family: var(--font-family);
    background: white;
    direction: rtl;
}

/* Glossary Content */
.glossary-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-lg);
}

.glossary-stats {
    text-align: center;
    margin-bottom: var(--spacing-lg);
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.glossary-category {
    margin-bottom: var(--spacing-xl);
}

.category-title {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: var(--spacing-sm);
}

.category-count {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: normal;
}

.category-items {
    display: grid;
    gap: var(--spacing-md);
}

/* Glossary Items */
.glossary-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: var(--spacing-lg);
    border-right: 4px solid var(--primary-color);
    transition: all 0.3s ease;
    position: relative;
}

.glossary-item:hover {
    background: #e9ecef;
    transform: translateY(-1px);
    box-shadow: var(--shadow-light);
}

.term-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.term-name {
    color: var(--primary-color);
    margin: 0;
    font-size: 1.1rem;
    font-weight: 700;
}

.term-arabic {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 600;
}

.term-full-name {
    color: var(--text-secondary);
    font-size: 0.85rem;
    margin-bottom: var(--spacing-sm);
    font-style: italic;
}

.term-definition {
    color: var(--text-primary);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
}

.term-actions {
    display: flex;
    gap: var(--spacing-sm);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.glossary-item:hover .term-actions {
    opacity: 1;
}

.copy-term-btn,
.highlight-term-btn {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.copy-term-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.highlight-term-btn:hover {
    background: var(--warning-color);
    color: white;
    border-color: var(--warning-color);
}

/* Terms in Content */
.glossary-term {
    background: linear-gradient(120deg, #a8e6cf 0%, #dcedc1 100%);
    padding: 2px 4px;
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 1px dotted var(--primary-color);
}

.glossary-term:hover {
    background: linear-gradient(120deg, #81c784 0%, #aed581 100%);
    transform: scale(1.05);
}

.highlighted-term {
    background: linear-gradient(120deg, #ffeb3b 0%, #ffc107 100%) !important;
    animation: highlight-pulse 2s ease-in-out;
}

@keyframes highlight-pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Term Popup */
.term-popup {
    position: absolute;
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    z-index: 1001;
    max-width: 350px;
    border: 1px solid var(--border-color);
}

.popup-content {
    padding: var(--spacing-md);
}

.popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
    font-size: 1.1rem;
}

.popup-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.popup-arabic {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: var(--spacing-sm);
    font-weight: 600;
}

.popup-definition {
    color: var(--text-primary);
    line-height: 1.5;
    margin-bottom: var(--spacing-md);
    font-size: 0.9rem;
}

.popup-actions {
    text-align: center;
}

.open-glossary-btn {
    font-size: 0.8rem;
    padding: var(--spacing-xs) var(--spacing-sm);
}

/* No Results */
.no-results {
    text-align: center;
    color: var(--text-secondary);
    padding: var(--spacing-xl);
}

.no-results i {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

/* Notification */
.glossary-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--success-color);
    color: white;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: 8px;
    box-shadow: var(--shadow-medium);
    z-index: 1002;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    font-weight: 600;
}

.glossary-notification.show {
    transform: translateX(0);
}

/* Responsive Glossary */
@media (max-width: 768px) {
    .glossary-toggle-btn {
        bottom: 15px;
        left: 15px;
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .glossary-toggle-btn span {
        display: none;
    }

    .glossary-modal {
        padding: var(--spacing-sm);
    }

    .glossary-modal-content {
        max-height: 95vh;
    }

    .glossary-header {
        padding: var(--spacing-md);
    }

    .glossary-header h3 {
        font-size: 1.1rem;
    }

    .glossary-search {
        padding: var(--spacing-md);
    }

    .glossary-content {
        padding: var(--spacing-md);
    }

    .term-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .term-actions {
        opacity: 1;
        margin-top: var(--spacing-sm);
    }

    .term-popup {
        max-width: 280px;
        left: 10px !important;
        right: 10px !important;
        width: auto !important;
    }

    .glossary-notification {
        right: 10px;
        left: 10px;
        text-align: center;
    }
}

/* Case Studies System Styles */
.case-study-container {
    margin: var(--spacing-xl) 0;
}

.case-study-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: var(--spacing-xl);
    color: white;
    text-align: center;
    box-shadow: var(--shadow-medium);
}

.case-study-header h3 {
    margin-bottom: var(--spacing-md);
    font-size: 1.4rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.case-study-description {
    margin-bottom: var(--spacing-lg);
    opacity: 0.9;
    font-size: 1.1rem;
    line-height: 1.6;
}

.case-study-start-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: 8px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.case-study-start-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

/* Case Study Modal */
.case-study-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: var(--spacing-md);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.case-study-modal.active {
    opacity: 1;
    visibility: visible;
}

.case-study-modal-content {
    background: white;
    border-radius: 12px;
    width: 100%;
    max-width: 900px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.case-study-modal.active .case-study-modal-content {
    transform: scale(1);
}

.case-study-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    background: var(--primary-color);
    color: white;
}

.case-study-modal-header h3 {
    margin: 0;
    font-size: 1.3rem;
}

.case-study-close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 4px;
    transition: background 0.3s ease;
}

.case-study-close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Progress Steps */
.case-study-progress {
    padding: var(--spacing-lg);
    background: #f8f9fa;
    border-bottom: 1px solid #eee;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.progress-steps::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background: #e0e0e0;
    z-index: 1;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 2;
    background: #f8f9fa;
    padding: var(--spacing-xs);
    border-radius: 8px;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e0e0e0;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: var(--spacing-xs);
    transition: all 0.3s ease;
}

.step-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    text-align: center;
    font-weight: 600;
}

.step.active .step-number {
    background: var(--primary-color);
    color: white;
}

.step.active .step-label {
    color: var(--primary-color);
}

.step.completed .step-number {
    background: var(--success-color);
    color: white;
}

.step.completed .step-number::after {
    content: '✓';
    font-size: 0.8rem;
}

/* Case Study Content */
.case-study-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-xl);
}

.case-study-step h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: var(--spacing-sm);
}

.scenario-content p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-primary);
    background: #f8f9fa;
    padding: var(--spacing-lg);
    border-radius: 8px;
    border-right: 4px solid var(--primary-color);
}

/* Challenges List */
.challenges-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.challenge-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: #fff5f5;
    border-radius: 8px;
    border-right: 4px solid var(--danger-color);
}

.challenge-number {
    background: var(--danger-color);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.challenge-text {
    color: var(--text-primary);
    line-height: 1.6;
}

/* Solutions List */
.solutions-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.solution-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: #f0f8ff;
    border-radius: 8px;
    border-right: 4px solid var(--primary-color);
}

.solution-number {
    background: var(--primary-color);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.solution-text {
    color: var(--text-primary);
    line-height: 1.6;
}

/* Outcomes List */
.outcomes-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.outcome-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: #f1f8e9;
    border-radius: 8px;
    border-right: 4px solid var(--success-color);
}

.outcome-item i {
    color: var(--success-color);
    font-size: 1.2rem;
    margin-top: 2px;
}

.outcome-text {
    color: var(--text-primary);
    line-height: 1.6;
    font-weight: 600;
}

/* Lessons List */
.lessons-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.lesson-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: #fff8e1;
    border-radius: 8px;
    border-right: 4px solid var(--warning-color);
}

.lesson-number {
    background: var(--warning-color);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.lesson-text {
    color: var(--text-primary);
    line-height: 1.6;
    font-weight: 500;
}

/* Case Study Completion */
.case-study-completion {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    padding: var(--spacing-xl);
    border-radius: 12px;
    text-align: center;
    margin-top: var(--spacing-lg);
}

.completion-message i {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
}

.completion-message h5 {
    margin-bottom: var(--spacing-sm);
    font-size: 1.3rem;
}

.case-study-completion-full {
    text-align: center;
    padding: var(--spacing-xl);
}

.completion-animation {
    margin-bottom: var(--spacing-xl);
}

.completion-animation i {
    font-size: 5rem;
    color: #FFD700;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-20px); }
    60% { transform: translateY(-10px); }
}

.case-study-completion-full h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    font-size: 1.5rem;
}

.case-study-completion-full p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    font-size: 1.1rem;
}

.completion-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

/* Case Study Navigation */
.case-study-navigation {
    display: flex;
    justify-content: space-between;
    padding: var(--spacing-lg);
    background: #f8f9fa;
    border-top: 1px solid #eee;
}

.case-study-navigation .btn {
    min-width: 120px;
}

/* Responsive Case Studies */
@media (max-width: 768px) {
    .case-study-modal {
        padding: var(--spacing-sm);
    }

    .case-study-modal-content {
        max-height: 95vh;
    }

    .case-study-modal-header {
        padding: var(--spacing-md);
    }

    .case-study-modal-header h3 {
        font-size: 1.1rem;
    }

    .case-study-progress {
        padding: var(--spacing-md);
    }

    .progress-steps {
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }

    .step {
        flex: 1;
        min-width: 80px;
    }

    .step-number {
        width: 30px;
        height: 30px;
        font-size: 0.8rem;
    }

    .step-label {
        font-size: 0.7rem;
    }

    .case-study-content {
        padding: var(--spacing-md);
    }

    .case-study-step h4 {
        font-size: 1.1rem;
    }

    .challenge-item,
    .solution-item,
    .outcome-item,
    .lesson-item {
        flex-direction: column;
        text-align: center;
    }

    .challenge-number,
    .solution-number,
    .lesson-number {
        align-self: center;
    }

    .case-study-navigation {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .case-study-navigation .btn {
        width: 100%;
    }

    .completion-actions {
        flex-direction: column;
    }

    .completion-actions .btn {
        width: 100%;
    }
}

/* Progress Tracker Styles */
.progress-tracker-btn {
    position: fixed;
    bottom: 80px;
    left: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 50px;
    padding: var(--spacing-md) var(--spacing-lg);
    box-shadow: var(--shadow-medium);
    cursor: pointer;
    z-index: 998;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
    min-width: 160px;
}

.progress-tracker-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-large);
}

.progress-indicator {
    margin-right: var(--spacing-sm);
}

.progress-circle {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: bold;
}

/* Progress Dashboard */
.progress-dashboard {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: var(--spacing-md);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.progress-dashboard.active {
    opacity: 1;
}

.progress-dashboard-content {
    background: white;
    border-radius: 12px;
    width: 100%;
    max-width: 1000px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.progress-dashboard.active .progress-dashboard-content {
    transform: scale(1);
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.dashboard-header h3 {
    margin: 0;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.dashboard-close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 4px;
    transition: background 0.3s ease;
}

.dashboard-close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Dashboard Tabs */
.dashboard-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #eee;
}

.tab-btn {
    flex: 1;
    padding: var(--spacing-md);
    background: none;
    border: none;
    cursor: pointer;
    font-family: var(--font-family);
    font-weight: 600;
    color: var(--text-secondary);
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.tab-btn:hover {
    background: #e9ecef;
    color: var(--primary-color);
}

.tab-btn.active {
    color: var(--primary-color);
    background: white;
    border-bottom-color: var(--primary-color);
}

/* Dashboard Content */
.dashboard-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-lg);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Overview Tab */
.overview-content {
    display: grid;
    gap: var(--spacing-xl);
}

.progress-summary {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: var(--spacing-xl);
    align-items: center;
}

.main-progress {
    display: flex;
    justify-content: center;
}

.progress-circle-large {
    position: relative;
    width: 150px;
    height: 150px;
}

.progress-circle-large svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
}

.progress-circle-large .progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.progress-text .percentage {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
}

.progress-text .label {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.progress-stats {
    display: grid;
    gap: var(--spacing-lg);
}

.stat-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: #f8f9fa;
    border-radius: 8px;
}

.stat-item i {
    font-size: 1.5rem;
    color: var(--primary-color);
    width: 30px;
    text-align: center;
}

.stat-content {
    display: flex;
    flex-direction: column;
}

.stat-value {
    font-size: 1.1rem;
    font-weight: bold;
    color: var(--text-primary);
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* Recent Activity */
.recent-activity h4,
.next-steps h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    font-size: 1.2rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: var(--spacing-sm);
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.activity-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-sm);
    background: #f8f9fa;
    border-radius: 6px;
}

.activity-item i {
    color: var(--primary-color);
    width: 20px;
    text-align: center;
}

.activity-content {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.activity-description {
    color: var(--text-primary);
    font-size: 0.9rem;
}

.activity-date {
    color: var(--text-secondary);
    font-size: 0.8rem;
}

/* Recommendations */
.recommendations {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.recommendation-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: #fff8e1;
    border-radius: 6px;
    border-right: 4px solid var(--warning-color);
}

.recommendation-item i {
    color: var(--warning-color);
    font-size: 1.2rem;
}

/* Parts Tab */
.parts-progress {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.part-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: var(--spacing-lg);
    border: 1px solid #e0e0e0;
}

.part-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.part-header h4 {
    flex: 1;
    margin: 0;
    color: var(--primary-color);
    font-size: 1.2rem;
}

.part-progress-bar {
    flex: 2;
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
}

.part-progress-bar .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transition: width 0.5s ease;
}

.part-percentage {
    font-weight: bold;
    color: var(--primary-color);
    min-width: 50px;
    text-align: center;
}

.chapters-list {
    display: grid;
    gap: var(--spacing-sm);
}

.chapter-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: white;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    transition: all 0.3s ease;
}

.chapter-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-light);
}

.chapter-item.completed {
    border-color: var(--success-color);
    background: #f1f8e9;
}

.chapter-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex: 1;
}

.chapter-title {
    color: var(--text-primary);
    font-weight: 500;
}

.chapter-indicators {
    display: flex;
    gap: var(--spacing-xs);
}

.chapter-indicators i {
    font-size: 0.9rem;
    color: var(--success-color);
}

.chapter-progress {
    font-weight: bold;
    color: var(--primary-color);
    min-width: 50px;
    text-align: center;
}

/* Achievements Tab */
.achievements-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.achievements-summary h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    text-align: center;
}

.achievements-progress-bar {
    height: 12px;
    background: #e0e0e0;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: var(--spacing-lg);
}

.achievements-progress-bar .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #FFD700, #FFA500);
    transition: width 0.5s ease;
}

.achievements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.achievement-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    border-radius: 12px;
    border: 2px solid #e0e0e0;
    transition: all 0.3s ease;
}

.achievement-card.unlocked {
    border-color: #FFD700;
    background: linear-gradient(135deg, #fff8e1, #fffbf0);
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.2);
}

.achievement-card.locked {
    opacity: 0.6;
    background: #f5f5f5;
}

.achievement-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.achievement-card.unlocked .achievement-icon {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: white;
}

.achievement-card.locked .achievement-icon {
    background: #e0e0e0;
    color: #999;
}

.achievement-info {
    flex: 1;
}

.achievement-info h5 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-primary);
    font-size: 1.1rem;
}

.achievement-info p {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.unlock-date {
    font-size: 0.8rem;
    color: var(--success-color);
    font-weight: 600;
}

/* Stats Tab */
.stats-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.stat-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    border: 1px solid #e0e0e0;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.stat-card .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.stat-card .stat-info {
    display: flex;
    flex-direction: column;
}

.stat-card .stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-color);
}

.stat-card .stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.detailed-stats h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: var(--spacing-sm);
}

.stats-table {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.stats-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: #f8f9fa;
    border-radius: 8px;
}

.stats-label {
    color: var(--text-primary);
    font-weight: 500;
}

.stats-value {
    color: var(--primary-color);
    font-weight: bold;
}

/* Achievement Notification */
.achievement-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: white;
    padding: var(--spacing-lg);
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
    z-index: 1002;
    transform: translateX(100%);
    transition: transform 0.5s ease;
    max-width: 350px;
}

.achievement-notification.show {
    transform: translateX(0);
}

.achievement-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.achievement-notification .achievement-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    flex-shrink: 0;
}

.achievement-text h4 {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: 1rem;
    opacity: 0.9;
}

.achievement-text h5 {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: 1.1rem;
}

.achievement-text p {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Responsive Progress Tracker */
@media (max-width: 768px) {
    .progress-tracker-btn {
        bottom: 70px;
        left: 15px;
        padding: var(--spacing-sm) var(--spacing-md);
        min-width: 120px;
    }

    .progress-tracker-btn span {
        display: none;
    }

    .progress-dashboard {
        padding: var(--spacing-sm);
    }

    .progress-dashboard-content {
        max-height: 95vh;
    }

    .dashboard-header {
        padding: var(--spacing-md);
    }

    .dashboard-header h3 {
        font-size: 1.1rem;
    }

    .dashboard-tabs {
        flex-wrap: wrap;
    }

    .tab-btn {
        flex: 1;
        min-width: 120px;
        font-size: 0.9rem;
        padding: var(--spacing-sm);
    }

    .dashboard-content {
        padding: var(--spacing-md);
    }

    .progress-summary {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--spacing-lg);
    }

    .progress-circle-large {
        width: 120px;
        height: 120px;
    }

    .progress-text .percentage {
        font-size: 1.5rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .achievements-grid {
        grid-template-columns: 1fr;
    }

    .achievement-card {
        flex-direction: column;
        text-align: center;
    }

    .stats-row {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-xs);
    }

    .achievement-notification {
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .achievement-content {
        flex-direction: column;
        text-align: center;
    }
}

/* Certificates System Styles */
.certificates-btn {
    position: fixed;
    bottom: 140px;
    left: 20px;
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    color: white;
    border: none;
    border-radius: 50px;
    padding: var(--spacing-md) var(--spacing-lg);
    box-shadow: var(--shadow-medium);
    cursor: pointer;
    z-index: 998;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
    min-width: 140px;
}

.certificates-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-large);
}

.certificates-indicator {
    position: relative;
}

.available-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #f44336;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: none;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: bold;
}

/* Certificates Modal */
.certificates-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: var(--spacing-md);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.certificates-modal.active {
    opacity: 1;
}

.certificates-modal-content {
    background: white;
    border-radius: 12px;
    width: 100%;
    max-width: 1200px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.certificates-modal.active .certificates-modal-content {
    transform: scale(1);
}

.certificates-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    color: white;
}

.certificates-header h3 {
    margin: 0;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.certificates-close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 4px;
    transition: background 0.3s ease;
}

.certificates-close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Certificates Tabs */
.certificates-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #eee;
}

.cert-tab-btn {
    flex: 1;
    padding: var(--spacing-md);
    background: none;
    border: none;
    cursor: pointer;
    font-family: var(--font-family);
    font-weight: 600;
    color: var(--text-secondary);
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.cert-tab-btn:hover {
    background: #e9ecef;
    color: #FFD700;
}

.cert-tab-btn.active {
    color: #FFD700;
    background: white;
    border-bottom-color: #FFD700;
}

/* Certificates Content */
.certificates-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-lg);
}

.cert-tab-content {
    display: none;
}

.cert-tab-content.active {
    display: block;
}

/* Certificates Grid */
.certificates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-xl);
}

.certificate-card {
    background: white;
    border-radius: 12px;
    padding: var(--spacing-lg);
    border: 2px solid #e0e0e0;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-light);
}

.certificate-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.certificate-card.earned {
    border-color: #4CAF50;
    background: linear-gradient(135deg, #f1f8e9, #e8f5e8);
}

.certificate-card.eligible {
    border-color: #FFD700;
    background: linear-gradient(135deg, #fff8e1, #fffbf0);
}

.certificate-card.locked {
    opacity: 0.6;
    background: #f5f5f5;
}

.certificate-preview {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    border-radius: 8px;
    border: 2px solid;
}

.certificate-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.certificate-info {
    flex: 1;
}

.certificate-info h4 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-primary);
    font-size: 1.2rem;
}

.certificate-info p {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.certificate-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.status-earned {
    color: #4CAF50;
    font-weight: 600;
}

.status-eligible {
    color: #FFD700;
    font-weight: 600;
}

.status-locked {
    color: #999;
    font-weight: 600;
}

.certificate-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.certificate-actions .btn {
    flex: 1;
    min-width: 120px;
}

.requirements-info {
    background: #f8f9fa;
    padding: var(--spacing-md);
    border-radius: 8px;
    border-right: 4px solid #2196F3;
}

.requirements-info h5 {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--primary-color);
    font-size: 1rem;
}

.requirements-info ul {
    margin: 0;
    padding-right: var(--spacing-lg);
}

.requirements-info li {
    margin-bottom: var(--spacing-xs);
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Badges Grid */
.badges-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.badge-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: white;
    border-radius: 12px;
    border: 2px solid #e0e0e0;
    transition: all 0.3s ease;
}

.badge-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.badge-card.earned {
    border-color: #4CAF50;
    background: linear-gradient(135deg, #f1f8e9, #e8f5e8);
}

.badge-card.locked {
    opacity: 0.6;
    background: #f5f5f5;
}

.badge-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.badge-info {
    flex: 1;
}

.badge-info h4 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-primary);
    font-size: 1.1rem;
}

.badge-info p {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.earned-date {
    font-size: 0.8rem;
    color: #4CAF50;
    font-weight: 600;
}

.not-earned {
    font-size: 0.8rem;
    color: #999;
    font-style: italic;
}

/* Progress Overview */
.progress-overview {
    display: grid;
    gap: var(--spacing-xl);
}

.overall-progress {
    text-align: center;
}

.overall-progress h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
    font-size: 1.3rem;
}

.progress-circle-container {
    display: flex;
    justify-content: center;
    margin-bottom: var(--spacing-lg);
}

.progress-circle {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background: conic-gradient(#4CAF50 0deg, #4CAF50 var(--progress-angle, 0deg), #e0e0e0 var(--progress-angle, 0deg));
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.progress-circle::before {
    content: '';
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: white;
    position: absolute;
}

.progress-percentage {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-color);
    z-index: 1;
}

.parts-progress {
    display: grid;
    gap: var(--spacing-md);
}

.part-progress-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: #f8f9fa;
    border-radius: 8px;
}

.part-progress-item h5 {
    flex: 1;
    margin: 0;
    color: var(--text-primary);
    font-size: 1rem;
}

.part-progress-item .progress-bar {
    flex: 2;
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
}

.part-progress-item .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transition: width 0.5s ease;
}

.part-progress-item .progress-text {
    font-weight: bold;
    color: var(--primary-color);
    min-width: 50px;
    text-align: center;
}

.achievements-summary {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
}

.summary-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    text-align: center;
}

.summary-item i {
    font-size: 2rem;
    color: var(--primary-color);
}

.summary-content {
    display: flex;
    flex-direction: column;
}

.summary-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-color);
}

.summary-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* Certificate Viewer */
.certificate-viewer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1001;
    padding: var(--spacing-md);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.certificate-viewer.active {
    opacity: 1;
}

.certificate-viewer-content {
    background: white;
    border-radius: 12px;
    width: 100%;
    max-width: 900px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
}

.certificate-viewer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    background: var(--primary-color);
    color: white;
}

.certificate-viewer-header h3 {
    margin: 0;
    font-size: 1.2rem;
}

.certificate-viewer-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 4px;
    transition: background 0.3s ease;
}

.certificate-viewer-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.certificate-display {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-xl);
    background: #f8f9fa;
}

/* Certificate Document */
.certificate-document {
    background: white;
    padding: var(--spacing-xl);
    border-radius: 12px;
    border: 8px solid;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    max-width: 800px;
    margin: 0 auto;
    font-family: 'Times New Roman', serif;
}

.certificate-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    border-bottom: 3px double #ccc;
    padding-bottom: var(--spacing-lg);
}

.certificate-logo {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
}

.certificate-header h1 {
    font-size: 2.5rem;
    margin: 0 0 var(--spacing-sm) 0;
    font-weight: bold;
}

.certificate-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    font-style: italic;
}

.certificate-body {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.certificate-intro {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

.recipient-name {
    font-size: 2rem;
    color: var(--primary-color);
    margin: var(--spacing-lg) 0;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: var(--spacing-sm);
    display: inline-block;
}

.certificate-description {
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xl);
}

.certificate-details {
    display: flex;
    justify-content: space-around;
    margin-bottom: var(--spacing-xl);
}

.detail-item {
    text-align: center;
}

.detail-label {
    display: block;
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.detail-value {
    font-size: 1.1rem;
    font-weight: bold;
    color: var(--primary-color);
}

.certificate-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 3px double #ccc;
    padding-top: var(--spacing-lg);
}

.certificate-signature {
    text-align: center;
}

.signature-line {
    width: 200px;
    height: 2px;
    background: #333;
    margin-bottom: var(--spacing-sm);
}

.certificate-signature p {
    margin: var(--spacing-xs) 0;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.certificate-seal {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 4px solid;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    background: rgba(255, 255, 255, 0.9);
}

.certificate-viewer-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: #f8f9fa;
    border-top: 1px solid #eee;
}

/* Notifications */
.eligibility-notification,
.certificate-earned-notification,
.badge-earned-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    z-index: 1002;
    transform: translateX(100%);
    transition: transform 0.5s ease;
    max-width: 400px;
    border: 2px solid #FFD700;
}

.eligibility-notification.show,
.certificate-earned-notification.show,
.badge-earned-notification.show {
    transform: translateX(0);
}

.eligibility-notification .notification-content,
.badge-earned-notification .notification-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
}

.certificate-earned-notification .notification-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: var(--spacing-xl);
}

.notification-icon,
.notification-animation {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.notification-animation {
    animation: bounce 2s infinite;
    font-size: 2rem;
    margin-bottom: var(--spacing-md);
}

.notification-text h3,
.notification-text h4,
.notification-text h5 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--primary-color);
}

.notification-text p {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--text-secondary);
}

.claim-now-btn {
    font-size: 0.9rem;
    padding: var(--spacing-sm) var(--spacing-md);
}

/* Responsive Certificates */
@media (max-width: 768px) {
    .certificates-btn {
        bottom: 130px;
        left: 15px;
        padding: var(--spacing-sm) var(--spacing-md);
        min-width: 120px;
    }

    .certificates-btn span {
        display: none;
    }

    .certificates-modal {
        padding: var(--spacing-sm);
    }

    .certificates-modal-content {
        max-height: 95vh;
    }

    .certificates-header {
        padding: var(--spacing-md);
    }

    .certificates-header h3 {
        font-size: 1.1rem;
    }

    .certificates-tabs {
        flex-wrap: wrap;
    }

    .cert-tab-btn {
        flex: 1;
        min-width: 100px;
        font-size: 0.9rem;
        padding: var(--spacing-sm);
    }

    .certificates-content {
        padding: var(--spacing-md);
    }

    .certificates-grid {
        grid-template-columns: 1fr;
    }

    .certificate-preview {
        flex-direction: column;
        text-align: center;
    }

    .certificate-actions {
        flex-direction: column;
    }

    .certificate-actions .btn {
        width: 100%;
    }

    .badges-grid {
        grid-template-columns: 1fr;
    }

    .badge-card {
        flex-direction: column;
        text-align: center;
    }

    .achievements-summary {
        grid-template-columns: 1fr;
    }

    .certificate-document {
        padding: var(--spacing-lg);
        font-size: 0.9rem;
    }

    .certificate-header h1 {
        font-size: 2rem;
    }

    .recipient-name {
        font-size: 1.5rem;
    }

    .certificate-details {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .certificate-footer {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }

    .certificate-viewer-actions {
        flex-direction: column;
    }

    .certificate-viewer-actions .btn {
        width: 100%;
    }

    .eligibility-notification,
    .certificate-earned-notification,
    .badge-earned-notification {
        right: 10px;
        left: 10px;
        max-width: none;
    }
}

/* Advanced Search Styles */
.search-filters {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: #f8f9fa;
    border-bottom: 1px solid #eee;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    min-width: 150px;
}

.filter-group label {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-secondary);
}

.filter-group select {
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-family: var(--font-family);
    background: white;
    direction: rtl;
}

.search-suggestions {
    padding: var(--spacing-lg);
    text-align: center;
}

.suggestions-header h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    font-size: 1rem;
}

.suggestion-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    justify-content: center;
}

.suggestion-tag {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: 20px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
}

.suggestion-tag:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-light);
}

.search-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: #f8f9fa;
    border-bottom: 1px solid #eee;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.results-count {
    font-weight: 600;
    color: var(--primary-color);
}

.search-time {
    font-style: italic;
}

.search-results-list {
    padding: var(--spacing-md);
}

.search-result-item {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    transition: all 0.3s ease;
    cursor: pointer;
}

.search-result-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-light);
    transform: translateY(-1px);
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.result-type {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.8rem;
    color: var(--text-secondary);
    background: #f0f0f0;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 12px;
}

.result-type i {
    font-size: 0.9rem;
}

.result-score {
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.result-title {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: 1.1rem;
}

.result-title a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

.result-title a:hover {
    color: var(--secondary-color);
}

.result-content {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
    font-size: 0.95rem;
}

.result-content mark {
    background: linear-gradient(120deg, #ffeb3b 0%, #ffc107 100%);
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: 600;
}

.result-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: var(--text-secondary);
    border-top: 1px solid #f0f0f0;
    padding-top: var(--spacing-sm);
}

.result-part {
    background: var(--secondary-color);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 10px;
    font-weight: 600;
}

.result-date {
    font-style: italic;
}

.no-results {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--text-secondary);
}

.no-results i {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

.no-results h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.no-results p {
    margin-bottom: var(--spacing-lg);
}

.no-results .search-suggestions {
    background: #f8f9fa;
    border-radius: 8px;
    margin-top: var(--spacing-lg);
}

.no-results .search-suggestions h5 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    font-size: 1rem;
}

/* Enhanced Search Overlay */
.search-overlay .search-container {
    max-width: 800px;
    width: 100%;
}

.search-overlay .search-header {
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-lg);
    border-radius: 12px 12px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.search-overlay .search-header h3 {
    margin: 0;
    font-size: 1.2rem;
}

.search-overlay .search-input-container {
    position: relative;
    padding: var(--spacing-lg);
    background: white;
}

.search-overlay .search-input-container i {
    position: absolute;
    right: var(--spacing-lg);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.search-overlay #search-input {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-xl) var(--spacing-md) var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    font-family: var(--font-family);
    direction: rtl;
    transition: border-color 0.3s ease;
}

.search-overlay #search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
}

.search-overlay .search-shortcut {
    position: absolute;
    left: var(--spacing-lg);
    top: 50%;
    transform: translateY(-50%);
    background: #f0f0f0;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 4px;
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.search-overlay .search-results {
    max-height: 60vh;
    overflow-y: auto;
    background: white;
    border-radius: 0 0 12px 12px;
}

/* Responsive Advanced Search */
@media (max-width: 768px) {
    .search-filters {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .filter-group {
        min-width: auto;
    }

    .suggestion-tags {
        justify-content: flex-start;
    }

    .search-stats {
        flex-direction: column;
        gap: var(--spacing-xs);
        text-align: center;
    }

    .result-header {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: flex-start;
    }

    .result-meta {
        flex-direction: column;
        gap: var(--spacing-xs);
        align-items: flex-start;
    }

    .search-overlay .search-container {
        margin: var(--spacing-sm);
        max-width: none;
    }

    .search-overlay .search-header {
        padding: var(--spacing-md);
    }

    .search-overlay .search-header h3 {
        font-size: 1rem;
    }

    .search-overlay .search-input-container {
        padding: var(--spacing-md);
    }

    .search-overlay .search-shortcut {
        display: none;
    }
}

/* Bookmarks System Styles */
.bookmarks-btn {
    position: fixed;
    bottom: 200px;
    left: 20px;
    background: linear-gradient(135deg, #9C27B0 0%, #673AB7 100%);
    color: white;
    border: none;
    border-radius: 50px;
    padding: var(--spacing-md) var(--spacing-lg);
    box-shadow: var(--shadow-medium);
    cursor: pointer;
    z-index: 998;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
    min-width: 160px;
}

.bookmarks-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-large);
}

.bookmarks-count {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 25px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: bold;
}

/* Page and Section Bookmark Buttons */
.page-bookmark-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    padding: var(--spacing-sm) var(--spacing-md);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.9rem;
    margin-right: var(--spacing-md);
}

.page-bookmark-btn:hover {
    background: var(--secondary-color);
    transform: translateY(-1px);
}

.page-bookmark-btn.bookmarked {
    background: #9C27B0;
}

.section-bookmark-btn {
    position: absolute;
    top: 0;
    left: -40px;
    background: rgba(156, 39, 176, 0.1);
    color: #9C27B0;
    border: 1px solid rgba(156, 39, 176, 0.3);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 0;
    font-size: 0.8rem;
}

.content-section:hover .section-bookmark-btn {
    opacity: 1;
}

.section-bookmark-btn:hover {
    background: rgba(156, 39, 176, 0.2);
    transform: scale(1.1);
}

.section-bookmark-btn.bookmarked {
    background: #9C27B0;
    color: white;
    opacity: 1;
}

/* Bookmarks Modal */
.bookmarks-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: var(--spacing-md);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.bookmarks-modal.active {
    opacity: 1;
}

.bookmarks-modal-content {
    background: white;
    border-radius: 12px;
    width: 100%;
    max-width: 1000px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.bookmarks-modal.active .bookmarks-modal-content {
    transform: scale(1);
}

.bookmarks-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, #9C27B0 0%, #673AB7 100%);
    color: white;
}

.bookmarks-header h3 {
    margin: 0;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.bookmarks-close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 4px;
    transition: background 0.3s ease;
}

.bookmarks-close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Bookmarks Toolbar */
.bookmarks-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    background: #f8f9fa;
    border-bottom: 1px solid #eee;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.bookmarks-search {
    position: relative;
    flex: 1;
    min-width: 250px;
}

.bookmarks-search i {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

#bookmarks-search {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-xl) var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-family: var(--font-family);
    direction: rtl;
    transition: border-color 0.3s ease;
}

#bookmarks-search:focus {
    outline: none;
    border-color: #9C27B0;
}

.bookmarks-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.bookmarks-actions .btn {
    font-size: 0.9rem;
    padding: var(--spacing-sm) var(--spacing-md);
}

/* Bookmarks Content */
.bookmarks-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-lg);
}

.bookmarks-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.bookmarks-stats .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    text-align: center;
}

.bookmarks-stats .stat-item i {
    font-size: 2rem;
    color: #9C27B0;
    margin-bottom: var(--spacing-sm);
}

.bookmarks-stats .stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.bookmarks-stats .stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* Bookmarks List */
.bookmarks-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.bookmark-category {
    background: white;
    border-radius: 12px;
    border: 1px solid #e0e0e0;
    overflow: hidden;
}

.category-title {
    background: #f8f9fa;
    padding: var(--spacing-md) var(--spacing-lg);
    margin: 0;
    color: var(--primary-color);
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    border-bottom: 1px solid #e0e0e0;
}

.category-count {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: normal;
}

.category-bookmarks {
    padding: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.bookmark-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: #f8f9fa;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.bookmark-item:hover {
    background: #e9ecef;
    border-color: #9C27B0;
    transform: translateY(-1px);
}

.bookmark-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #9C27B0, #673AB7);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
}

.bookmark-content {
    flex: 1;
    min-width: 0;
}

.bookmark-title {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: 1rem;
}

.bookmark-title a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

.bookmark-title a:hover {
    color: #9C27B0;
}

.bookmark-section {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-style: italic;
}

.bookmark-meta {
    display: flex;
    gap: var(--spacing-md);
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.bookmark-type {
    background: #9C27B0;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 600;
}

.bookmark-actions {
    display: flex;
    gap: var(--spacing-xs);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.bookmark-item:hover .bookmark-actions {
    opacity: 1;
}

.bookmark-action-btn {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: var(--spacing-xs);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.bookmark-action-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.delete-bookmark-btn:hover {
    background: var(--danger-color);
    border-color: var(--danger-color);
}

/* No Bookmarks State */
.no-bookmarks {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--text-secondary);
}

.no-bookmarks i {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
    color: #9C27B0;
}

.no-bookmarks h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-size: 1.2rem;
}

.no-bookmarks p {
    margin-bottom: var(--spacing-xl);
    font-size: 1rem;
}

.bookmark-tips {
    background: #f8f9fa;
    border-radius: 8px;
    padding: var(--spacing-lg);
    text-align: right;
    max-width: 400px;
    margin: 0 auto;
}

.bookmark-tips h5 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    font-size: 1rem;
}

.bookmark-tips ul {
    margin: 0;
    padding-right: var(--spacing-lg);
    list-style: none;
}

.bookmark-tips li {
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 0.9rem;
}

.bookmark-tips li::before {
    content: '•';
    color: #9C27B0;
    font-weight: bold;
    font-size: 1.2rem;
}

.bookmark-tips kbd {
    background: #e0e0e0;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.8rem;
    font-family: monospace;
}

/* Bookmark Notifications */
.bookmark-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: 8px;
    box-shadow: var(--shadow-medium);
    z-index: 1002;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    max-width: 350px;
}

.bookmark-notification.show {
    transform: translateX(0);
}

.bookmark-notification.success {
    background: #4CAF50;
    color: white;
}

.bookmark-notification.error {
    background: #f44336;
    color: white;
}

.bookmark-notification.info {
    background: #2196F3;
    color: white;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.notification-content i {
    font-size: 1.2rem;
}

/* Responsive Bookmarks */
@media (max-width: 768px) {
    .bookmarks-btn {
        bottom: 190px;
        left: 15px;
        padding: var(--spacing-sm) var(--spacing-md);
        min-width: 120px;
    }

    .bookmarks-btn span {
        display: none;
    }

    .bookmarks-modal {
        padding: var(--spacing-sm);
    }

    .bookmarks-modal-content {
        max-height: 95vh;
    }

    .bookmarks-header {
        padding: var(--spacing-md);
    }

    .bookmarks-header h3 {
        font-size: 1.1rem;
    }

    .bookmarks-toolbar {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
    }

    .bookmarks-search {
        min-width: auto;
    }

    .bookmarks-actions {
        justify-content: center;
    }

    .bookmarks-actions .btn {
        flex: 1;
        min-width: 80px;
    }

    .bookmarks-content {
        padding: var(--spacing-md);
    }

    .bookmarks-stats {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .bookmark-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }

    .bookmark-actions {
        opacity: 1;
        justify-content: center;
    }

    .bookmark-meta {
        justify-content: center;
    }

    .section-bookmark-btn {
        position: static;
        opacity: 1;
        margin-right: var(--spacing-sm);
    }

    .page-bookmark-btn {
        position: fixed;
        bottom: 20px;
        right: 20px;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        padding: 0;
        justify-content: center;
        z-index: 999;
    }

    .page-bookmark-btn span {
        display: none;
    }

    .bookmark-notification {
        right: 10px;
        left: 10px;
        max-width: none;
    }
}

/* Resources Library Styles */
.resources-btn {
    position: fixed;
    bottom: 260px;
    left: 20px;
    background: linear-gradient(135deg, #FF5722 0%, #E91E63 100%);
    color: white;
    border: none;
    border-radius: 50px;
    padding: var(--spacing-md) var(--spacing-lg);
    box-shadow: var(--shadow-medium);
    cursor: pointer;
    z-index: 998;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
    min-width: 160px;
}

.resources-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-large);
}

.resources-indicator {
    position: relative;
}

.new-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #4CAF50;
    color: white;
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 0.7rem;
    font-weight: bold;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Resources Modal */
.resources-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: var(--spacing-md);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.resources-modal.active {
    opacity: 1;
}

.resources-modal-content {
    background: white;
    border-radius: 12px;
    width: 100%;
    max-width: 1200px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.resources-modal.active .resources-modal-content {
    transform: scale(1);
}

.resources-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, #FF5722 0%, #E91E63 100%);
    color: white;
}

.resources-header h3 {
    margin: 0;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.resources-close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 4px;
    transition: background 0.3s ease;
}

.resources-close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Resources Tabs */
.resources-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #eee;
    overflow-x: auto;
}

.resource-tab-btn {
    flex: 1;
    padding: var(--spacing-md);
    background: none;
    border: none;
    cursor: pointer;
    font-family: var(--font-family);
    font-weight: 600;
    color: var(--text-secondary);
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
    white-space: nowrap;
    min-width: 120px;
}

.resource-tab-btn:hover {
    background: #e9ecef;
    color: #FF5722;
}

.resource-tab-btn.active {
    color: #FF5722;
    background: white;
    border-bottom-color: #FF5722;
}

/* Resources Content */
.resources-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-lg);
}

.resources-search {
    position: relative;
    margin-bottom: var(--spacing-xl);
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.resources-search i {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

#resources-search {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-xl) var(--spacing-md) var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-family: var(--font-family);
    direction: rtl;
    transition: border-color 0.3s ease;
}

#resources-search:focus {
    outline: none;
    border-color: #FF5722;
}

/* Resources Grid */
.resources-grid {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.resource-category {
    background: white;
    border-radius: 12px;
    border: 1px solid #e0e0e0;
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

.category-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
}

.category-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.3rem;
}

.category-header h4 {
    flex: 1;
    margin: 0;
    color: var(--text-primary);
    font-size: 1.2rem;
}

.category-count {
    background: #e0e0e0;
    color: var(--text-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 12px;
    font-size: 0.9rem;
    font-weight: 600;
}

.category-resources {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
}

/* Resource Items */
.resource-item {
    background: #f8f9fa;
    border-radius: 12px;
    padding: var(--spacing-lg);
    border: 1px solid #e0e0e0;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.resource-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    border-color: #FF5722;
}

.resource-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.file-icon {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.3rem;
}

.file-type-badge {
    background: rgba(0, 0, 0, 0.1);
    color: var(--text-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.resource-content {
    flex: 1;
}

.resource-title {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    line-height: 1.4;
}

.resource-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
    font-size: 0.95rem;
}

.resource-meta {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.resource-meta span {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.resource-meta i {
    font-size: 0.9rem;
}

.resource-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.resource-actions .btn {
    flex: 1;
    font-size: 0.9rem;
    padding: var(--spacing-sm) var(--spacing-md);
}

/* Download History */
.download-history-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.download-history-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: white;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    transition: all 0.3s ease;
}

.download-history-item:hover {
    border-color: #FF5722;
    box-shadow: var(--shadow-light);
}

.download-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #FF5722;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.download-content {
    flex: 1;
}

.download-content h5 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-primary);
    font-size: 1rem;
}

.download-meta {
    display: flex;
    gap: var(--spacing-md);
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.download-actions .btn {
    font-size: 0.8rem;
    padding: var(--spacing-xs) var(--spacing-sm);
}

/* No Downloads/Results */
.no-downloads,
.no-results {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--text-secondary);
}

.no-downloads i,
.no-results i {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
    color: #FF5722;
}

.no-downloads h4,
.no-results h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-size: 1.2rem;
}

.no-downloads p,
.no-results p {
    margin-bottom: var(--spacing-lg);
    font-size: 1rem;
}

/* Resource Preview Modal */
.resource-preview-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1001;
    padding: var(--spacing-md);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.resource-preview-modal.active {
    opacity: 1;
}

.preview-modal-content {
    background: white;
    border-radius: 12px;
    width: 100%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    background: #FF5722;
    color: white;
}

.preview-header h3 {
    margin: 0;
    font-size: 1.2rem;
}

.preview-close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 4px;
    transition: background 0.3s ease;
}

.preview-close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.preview-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-lg);
}

.resource-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: #f8f9fa;
    border-radius: 8px;
}

.info-item {
    font-size: 0.9rem;
}

.info-item strong {
    color: var(--primary-color);
}

.resource-description h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.resource-description p {
    line-height: 1.6;
    color: var(--text-secondary);
}

.preview-placeholder {
    text-align: center;
    padding: var(--spacing-xl);
    background: #f8f9fa;
    border-radius: 8px;
    margin-top: var(--spacing-lg);
}

.preview-placeholder i {
    font-size: 3rem;
    color: #FF5722;
    margin-bottom: var(--spacing-md);
}

.preview-placeholder h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.preview-placeholder p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

.preview-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: #f8f9fa;
    border-top: 1px solid #eee;
}

/* Resource Notifications */
.resource-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: 8px;
    box-shadow: var(--shadow-medium);
    z-index: 1002;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    max-width: 350px;
}

.resource-notification.show {
    transform: translateX(0);
}

.resource-notification.success {
    background: #4CAF50;
    color: white;
}

.resource-notification.info {
    background: #2196F3;
    color: white;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.notification-content i {
    font-size: 1.2rem;
}

/* Responsive Resources */
@media (max-width: 768px) {
    .resources-btn {
        bottom: 250px;
        left: 15px;
        padding: var(--spacing-sm) var(--spacing-md);
        min-width: 120px;
    }

    .resources-btn span {
        display: none;
    }

    .resources-modal {
        padding: var(--spacing-sm);
    }

    .resources-modal-content {
        max-height: 95vh;
    }

    .resources-header {
        padding: var(--spacing-md);
    }

    .resources-header h3 {
        font-size: 1.1rem;
    }

    .resources-tabs {
        flex-wrap: wrap;
    }

    .resource-tab-btn {
        flex: 1;
        min-width: 100px;
        font-size: 0.9rem;
        padding: var(--spacing-sm);
    }

    .resources-content {
        padding: var(--spacing-md);
    }

    .category-resources {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .resource-item {
        padding: var(--spacing-md);
    }

    .resource-actions {
        flex-direction: column;
    }

    .resource-actions .btn {
        width: 100%;
    }

    .download-history-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }

    .download-meta {
        justify-content: center;
    }

    .preview-modal-content {
        max-width: 95%;
        max-height: 90vh;
    }

    .preview-actions {
        flex-direction: column;
    }

    .preview-actions .btn {
        width: 100%;
    }

    .resource-notification {
        right: 10px;
        left: 10px;
        max-width: none;
    }
}

/* Performance Optimizations & Final Enhancements */

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--shadow-medium);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-large);
}

/* Global Loading Indicator */
.global-loading-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.global-loading-indicator.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.global-loading-indicator span {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 1.1rem;
}

/* Keyboard Shortcuts Modal */
.keyboard-shortcuts-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.keyboard-shortcuts-modal.active {
    opacity: 1;
    visibility: visible;
}

.shortcuts-modal-content {
    background: white;
    border-radius: 12px;
    padding: var(--spacing-xl);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.keyboard-shortcuts-modal.active .shortcuts-modal-content {
    transform: scale(1);
}

.shortcuts-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--primary-color);
}

.shortcuts-header h3 {
    margin: 0;
    color: var(--primary-color);
    font-size: 1.3rem;
}

.shortcuts-close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-secondary);
    padding: var(--spacing-xs);
    border-radius: 4px;
    transition: all 0.3s ease;
}

.shortcuts-close-btn:hover {
    background: #f0f0f0;
    color: var(--primary-color);
}

.shortcuts-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.shortcut-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: #f8f9fa;
    border-radius: 8px;
    border-right: 4px solid var(--primary-color);
}

.shortcut-item kbd {
    background: #e0e0e0;
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 4px 8px;
    font-family: monospace;
    font-size: 0.9rem;
    margin: 0 2px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.shortcut-item span {
    color: var(--text-primary);
    font-weight: 500;
}

/* Touch Device Enhancements */
.touch-device .hover-effect:hover {
    transform: none;
}

.touch-device .section-bookmark-btn {
    opacity: 1;
    position: static;
    margin-right: var(--spacing-sm);
}

.touch-device .bookmark-actions {
    opacity: 1;
}

/* Lazy Loading Images */
img.lazy {
    opacity: 0;
    transition: opacity 0.3s ease;
}

img.lazy.loaded {
    opacity: 1;
}

/* Font Loading Optimization */
.fonts-loaded {
    font-display: swap;
}

/* Improved Focus Styles */
*:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

button:focus,
.btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(44, 90, 160, 0.2);
}

input:focus,
textarea:focus,
select:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(44, 90, 160, 0.1);
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #000080;
        --secondary-color: #800080;
        --text-primary: #000000;
        --text-secondary: #333333;
        --border-color: #000000;
    }

    .btn {
        border: 2px solid currentColor;
    }

    .card,
    .content-section {
        border: 2px solid var(--border-color);
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .loading-spinner {
        animation: none;
        border-top-color: var(--primary-color);
    }
}

/* Print Styles */
@media print {
    .header,
    .footer,
    .sidebar,
    .floating-buttons,
    .back-to-top,
    .progress-tracker-btn,
    .bookmarks-btn,
    .certificates-btn,
    .resources-btn,
    .glossary-toggle-btn {
        display: none !important;
    }

    .main-content {
        margin: 0;
        padding: 0;
        max-width: none;
    }

    .content-section {
        break-inside: avoid;
        page-break-inside: avoid;
    }

    h1, h2, h3, h4, h5, h6 {
        break-after: avoid;
        page-break-after: avoid;
    }

    .quiz-container,
    .checklist-container,
    .case-study-container {
        break-inside: avoid;
        page-break-inside: avoid;
    }

    a[href^="http"]:after {
        content: " (" attr(href) ")";
        font-size: 0.8em;
        color: #666;
    }

    .btn {
        border: 1px solid #ccc;
        background: white !important;
        color: black !important;
    }
}

/* Dark Mode Support (Future Enhancement) */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: #1a1a1a;
        --bg-secondary: #2d2d2d;
        --text-primary: #ffffff;
        --text-secondary: #cccccc;
        --border-color: #444444;
    }

    body {
        background: var(--bg-primary);
        color: var(--text-primary);
    }

    .card,
    .content-section {
        background: var(--bg-secondary);
        border-color: var(--border-color);
    }

    .global-loading-indicator {
        background: rgba(26, 26, 26, 0.95);
    }
}

/* Responsive Enhancements for Small Screens */
@media (max-width: 480px) {
    .floating-buttons {
        position: fixed;
        bottom: 10px;
        left: 10px;
        right: 10px;
        display: flex;
        justify-content: space-between;
        z-index: 998;
    }

    .progress-tracker-btn,
    .bookmarks-btn,
    .certificates-btn,
    .resources-btn,
    .glossary-toggle-btn {
        position: static;
        flex: 1;
        margin: 0 2px;
        min-width: auto;
        padding: var(--spacing-sm);
        border-radius: 8px;
    }

    .progress-tracker-btn span,
    .bookmarks-btn span,
    .certificates-btn span,
    .resources-btn span,
    .glossary-toggle-btn span {
        display: none;
    }

    .back-to-top {
        bottom: 70px;
        right: 10px;
        width: 40px;
        height: 40px;
    }

    .shortcuts-modal-content {
        padding: var(--spacing-md);
        width: 95%;
    }

    .shortcut-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }
}

/* Performance Optimizations */
.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

@media (prefers-reduced-motion: reduce) {
    html {
        scroll-behavior: auto;
    }
}

/* Content Loading States */
.content-loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.content-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Error States */
.error-state {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--danger-color);
}

.error-state i {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
}

.error-state h4 {
    margin-bottom: var(--spacing-sm);
}

.error-state p {
    margin-bottom: var(--spacing-lg);
    color: var(--text-secondary);
}

/* Success States */
.success-state {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--success-color);
}

.success-state i {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
}

/* Utility Classes */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 10000;
}

.skip-link:focus {
    top: 6px;
}

.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.mb-0 { margin-bottom: 0; }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

.flex-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.flex-column {
    flex-direction: column;
}

.flex-wrap {
    flex-wrap: wrap;
}

.w-100 { width: 100%; }
.h-100 { height: 100%; }

.border-radius { border-radius: 8px; }
.border-radius-lg { border-radius: 12px; }

.shadow-sm { box-shadow: var(--shadow-light); }
.shadow-md { box-shadow: var(--shadow-medium); }
.shadow-lg { box-shadow: var(--shadow-large); }

.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--text-secondary); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }

.bg-primary { background: var(--primary-color); }
.bg-secondary { background: var(--secondary-color); }
.bg-light { background: #f8f9fa; }
.bg-white { background: white; }
