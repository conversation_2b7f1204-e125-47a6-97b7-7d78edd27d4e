/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Colors */
    --primary-color: #2c5aa0;
    --secondary-color: #1e3a5f;
    --accent-color: #4a90e2;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-color: #f8f9fa;
    --dark-color: #2c3e50;
    --text-primary: #2c3e50;
    --text-secondary: #7f8c8d;
    --border-color: #e9ecef;
    --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 20px rgba(0,0,0,0.15);
    --shadow-heavy: 0 8px 30px rgba(0,0,0,0.2);
    
    /* Typography */
    --font-family: 'Noto Sans Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: #ffffff;
    direction: rtl;
    text-align: right;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--accent-color);
}

/* Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.section-title {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
    position: relative;
    color: var(--primary-color);
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    right: 50%;
    transform: translateX(50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: var(--radius-sm);
}

/* Header and Navigation */
.main-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-light);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    transition: var(--transition-normal);
}

.navbar {
    padding: var(--spacing-md) 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
}

.nav-logo i {
    font-size: var(--font-size-2xl);
}

.nav-menu {
    display: flex;
    gap: var(--spacing-xl);
    align-items: center;
}

.nav-link {
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    background-color: var(--primary-color);
    color: white;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.nav-toggle .bar {
    width: 25px;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 120px 0 var(--spacing-3xl);
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
}

.hero-content {
    animation: fadeInUp 1s ease;
}

.hero-title {
    margin-bottom: var(--spacing-lg);
}

.title-main {
    display: block;
    font-size: var(--font-size-5xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.title-sub {
    display: block;
    font-size: var(--font-size-3xl);
    font-weight: 500;
    color: var(--secondary-color);
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: 1.8;
}

.hero-author {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.8);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-light);
}

.hero-author i {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
}

.author-affiliation {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-lg);
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition-normal);
    border: none;
    cursor: pointer;
    font-size: var(--font-size-base);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    box-shadow: var(--shadow-medium);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
    color: white;
}

.btn-secondary {
    background: white;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    box-shadow: var(--shadow-light);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.medical-illustration {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    padding: var(--spacing-2xl);
}

.medical-illustration i {
    font-size: 4rem;
    color: var(--primary-color);
    opacity: 0.8;
    transition: var(--transition-normal);
    animation: float 3s ease-in-out infinite;
}

.medical-illustration i:nth-child(2) {
    animation-delay: 0.5s;
}

.medical-illustration i:nth-child(3) {
    animation-delay: 1s;
}

.medical-illustration i:nth-child(4) {
    animation-delay: 1.5s;
}

.medical-illustration i:hover {
    transform: scale(1.1);
    color: var(--accent-color);
}

/* About Section */
.about {
    padding: var(--spacing-3xl) 0;
    background: white;
}

.about-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.about-card {
    text-align: center;
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    background: white;
    box-shadow: var(--shadow-light);
    transition: var(--transition-normal);
    border: 1px solid var(--border-color);
}

.about-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.card-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-lg);
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.card-icon i {
    font-size: var(--font-size-2xl);
    color: white;
}

.about-card h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

/* Contents Overview */
.contents-overview {
    padding: var(--spacing-3xl) 0;
    background: var(--light-color);
}

.parts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
}

.part-card {
    background: white;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-medium);
    transition: var(--transition-normal);
    border: 1px solid var(--border-color);
}

.part-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
}

.part-header {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    padding: var(--spacing-xl);
    text-align: center;
}

.part-number {
    display: block;
    font-size: var(--font-size-sm);
    opacity: 0.9;
    margin-bottom: var(--spacing-sm);
}

.part-header h3 {
    margin: 0;
    color: white;
}

.part-content {
    padding: var(--spacing-xl);
}

.chapter-list {
    list-style: none;
    margin-bottom: var(--spacing-lg);
}

.chapter-list li {
    margin-bottom: var(--spacing-sm);
}

.chapter-list a {
    display: block;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.chapter-list a:hover {
    background: var(--light-color);
    color: var(--primary-color);
}

.part-link {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    background: var(--primary-color);
    color: white;
    font-weight: 600;
    transition: var(--transition-fast);
}

.part-link:hover {
    background: var(--secondary-color);
    color: white;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Author Section */
.author {
    padding: var(--spacing-3xl) 0;
    background: white;
}

.author-card {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    box-shadow: var(--shadow-medium);
}

.author-info h3 {
    color: var(--primary-color);
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-sm);
}

.author-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: var(--spacing-xs);
}

.author-institution {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
}

.author-description {
    font-size: var(--font-size-base);
    line-height: 1.8;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Quick Access */
.quick-access {
    padding: var(--spacing-3xl) 0;
    background: var(--light-color);
}

.quick-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    max-width: 800px;
    margin: 0 auto;
}

.quick-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xl);
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-light);
    transition: var(--transition-normal);
    text-decoration: none;
    color: var(--text-primary);
}

.quick-link:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
    color: var(--primary-color);
}

.quick-link i {
    font-size: var(--font-size-2xl);
    color: var(--primary-color);
}

.quick-link span {
    font-weight: 600;
}

/* Footer */
.main-footer {
    background: var(--dark-color);
    color: white;
    padding: var(--spacing-3xl) 0 var(--spacing-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.footer-section h4 {
    color: white;
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-lg);
}

.footer-section p {
    color: #bdc3c7;
    margin-bottom: var(--spacing-sm);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: var(--spacing-sm);
}

.footer-section ul li a {
    color: #bdc3c7;
    transition: var(--transition-fast);
}

.footer-section ul li a:hover {
    color: white;
}

.footer-bottom {
    text-align: center;
    padding-top: var(--spacing-lg);
    border-top: 1px solid #34495e;
    color: #bdc3c7;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 100%;
        right: 0;
        width: 100%;
        background: white;
        flex-direction: column;
        padding: var(--spacing-lg);
        box-shadow: var(--shadow-medium);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: var(--transition-normal);
    }

    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--spacing-xl);
    }

    .title-main {
        font-size: var(--font-size-3xl);
    }

    .title-sub {
        font-size: var(--font-size-xl);
    }

    .hero-subtitle {
        font-size: var(--font-size-lg);
    }

    .hero-buttons {
        justify-content: center;
    }

    .about-grid {
        grid-template-columns: 1fr;
    }

    .parts-grid {
        grid-template-columns: 1fr;
    }

    .quick-links {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--spacing-md);
    }

    .hero {
        padding: 100px 0 var(--spacing-2xl);
    }

    .title-main {
        font-size: var(--font-size-2xl);
    }

    .title-sub {
        font-size: var(--font-size-lg);
    }

    .btn {
        padding: var(--spacing-sm) var(--spacing-lg);
        font-size: var(--font-size-sm);
    }

    .quick-links {
        grid-template-columns: 1fr;
    }

    .parts-grid {
        gap: var(--spacing-lg);
    }

    .part-card {
        margin: 0 var(--spacing-sm);
    }
}

/* Print Styles */
@media print {
    .main-header,
    .nav-toggle,
    .hero-buttons,
    .quick-access,
    .main-footer {
        display: none;
    }

    .hero {
        padding: var(--spacing-lg) 0;
        background: white;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
    }

    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
    }

    .part-card {
        page-break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #000080;
        --secondary-color: #000000;
        --text-primary: #000000;
        --text-secondary: #333333;
        --border-color: #000000;
    }

    .btn-primary {
        background: #000080;
        border: 2px solid #000080;
    }

    .btn-secondary {
        background: white;
        color: #000080;
        border: 2px solid #000080;
    }
}

/* Focus styles for accessibility */
a:focus,
button:focus,
.btn:focus {
    outline: 3px solid var(--accent-color);
    outline-offset: 2px;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Selection styles */
::selection {
    background: var(--primary-color);
    color: white;
}

/* Search Overlay Styles */
.search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    z-index: 2000;
    display: none;
    align-items: flex-start;
    justify-content: center;
    padding-top: 10vh;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.search-overlay.active {
    opacity: 1;
}

.search-container {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-heavy);
    width: 90%;
    max-width: 600px;
    max-height: 70vh;
    overflow: hidden;
    transform: translateY(-20px);
    transition: transform 0.3s ease;
}

.search-overlay.active .search-container {
    transform: translateY(0);
}

.search-header {
    display: flex;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.search-header input {
    flex: 1;
    border: none;
    outline: none;
    font-size: var(--font-size-lg);
    font-family: var(--font-family);
    padding: var(--spacing-sm);
    background: transparent;
    color: var(--text-primary);
    direction: rtl;
    text-align: right;
}

.search-header input::placeholder {
    color: var(--text-secondary);
}

.search-close {
    background: none;
    border: none;
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
    margin-right: var(--spacing-md);
}

.search-close:hover {
    background: var(--light-color);
    color: var(--text-primary);
}

.search-results {
    max-height: 50vh;
    overflow-y: auto;
    padding: var(--spacing-lg);
}

.search-placeholder,
.search-no-results {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-secondary);
}

.search-placeholder i,
.search-no-results i {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
    opacity: 0.5;
}

.search-result-item {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition-fast);
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-item:hover {
    background: var(--light-color);
}

.search-result-item h4 {
    margin-bottom: var(--spacing-sm);
}

.search-result-item h4 a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
}

.search-result-item h4 a:hover {
    color: var(--accent-color);
}

.search-result-item p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin: 0;
}

/* Keyboard navigation styles */
.keyboard-navigation *:focus {
    outline: 3px solid var(--accent-color);
    outline-offset: 2px;
}

.skip-link:focus {
    top: 6px !important;
}
