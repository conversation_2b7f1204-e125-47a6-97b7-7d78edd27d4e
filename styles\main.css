/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Colors */
    --primary-color: #2c5aa0;
    --secondary-color: #1e3a5f;
    --accent-color: #4a90e2;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-color: #f8f9fa;
    --dark-color: #2c3e50;
    --text-primary: #2c3e50;
    --text-secondary: #7f8c8d;
    --border-color: #e9ecef;
    --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 20px rgba(0,0,0,0.15);
    --shadow-heavy: 0 8px 30px rgba(0,0,0,0.2);
    
    /* Typography */
    --font-family: 'Noto Sans Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: #ffffff;
    direction: rtl;
    text-align: right;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--accent-color);
}

/* Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.section-title {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
    position: relative;
    color: var(--primary-color);
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    right: 50%;
    transform: translateX(50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: var(--radius-sm);
}

/* Header and Navigation */
.main-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-light);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    transition: var(--transition-normal);
}

.navbar {
    padding: var(--spacing-md) 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
}

.nav-logo i {
    font-size: var(--font-size-2xl);
}

.nav-menu {
    display: flex;
    gap: var(--spacing-xl);
    align-items: center;
}

.nav-link {
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    background-color: var(--primary-color);
    color: white;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.nav-toggle .bar {
    width: 25px;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 120px 0 var(--spacing-3xl);
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
}

.hero-content {
    animation: fadeInUp 1s ease;
}

.hero-title {
    margin-bottom: var(--spacing-lg);
}

.title-main {
    display: block;
    font-size: var(--font-size-5xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.title-sub {
    display: block;
    font-size: var(--font-size-3xl);
    font-weight: 500;
    color: var(--secondary-color);
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: 1.8;
}

.hero-author {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.8);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-light);
}

.hero-author i {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
}

.author-affiliation {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-lg);
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition-normal);
    border: none;
    cursor: pointer;
    font-size: var(--font-size-base);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    box-shadow: var(--shadow-medium);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
    color: white;
}

.btn-secondary {
    background: white;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    box-shadow: var(--shadow-light);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.medical-illustration {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    padding: var(--spacing-2xl);
}

.medical-illustration i {
    font-size: 4rem;
    color: var(--primary-color);
    opacity: 0.8;
    transition: var(--transition-normal);
    animation: float 3s ease-in-out infinite;
}

.medical-illustration i:nth-child(2) {
    animation-delay: 0.5s;
}

.medical-illustration i:nth-child(3) {
    animation-delay: 1s;
}

.medical-illustration i:nth-child(4) {
    animation-delay: 1.5s;
}

.medical-illustration i:hover {
    transform: scale(1.1);
    color: var(--accent-color);
}

/* About Section */
.about {
    padding: var(--spacing-3xl) 0;
    background: white;
}

.about-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.about-card {
    text-align: center;
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    background: white;
    box-shadow: var(--shadow-light);
    transition: var(--transition-normal);
    border: 1px solid var(--border-color);
}

.about-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.card-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-lg);
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.card-icon i {
    font-size: var(--font-size-2xl);
    color: white;
}

.about-card h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

/* Contents Overview */
.contents-overview {
    padding: var(--spacing-3xl) 0;
    background: var(--light-color);
}

.parts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
}

.part-card {
    background: white;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-medium);
    transition: var(--transition-normal);
    border: 1px solid var(--border-color);
}

.part-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
}

.part-header {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    padding: var(--spacing-xl);
    text-align: center;
}

.part-number {
    display: block;
    font-size: var(--font-size-sm);
    opacity: 0.9;
    margin-bottom: var(--spacing-sm);
}

.part-header h3 {
    margin: 0;
    color: white;
}

.part-content {
    padding: var(--spacing-xl);
}

.chapter-list {
    list-style: none;
    margin-bottom: var(--spacing-lg);
}

.chapter-list li {
    margin-bottom: var(--spacing-sm);
}

.chapter-list a {
    display: block;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.chapter-list a:hover {
    background: var(--light-color);
    color: var(--primary-color);
}

.part-link {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    background: var(--primary-color);
    color: white;
    font-weight: 600;
    transition: var(--transition-fast);
}

.part-link:hover {
    background: var(--secondary-color);
    color: white;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Author Section */
.author {
    padding: var(--spacing-3xl) 0;
    background: white;
}

.author-card {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    box-shadow: var(--shadow-medium);
}

.author-info h3 {
    color: var(--primary-color);
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-sm);
}

.author-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: var(--spacing-xs);
}

.author-institution {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
}

.author-description {
    font-size: var(--font-size-base);
    line-height: 1.8;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Quick Access */
.quick-access {
    padding: var(--spacing-3xl) 0;
    background: var(--light-color);
}

.quick-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    max-width: 800px;
    margin: 0 auto;
}

.quick-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xl);
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-light);
    transition: var(--transition-normal);
    text-decoration: none;
    color: var(--text-primary);
}

.quick-link:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
    color: var(--primary-color);
}

.quick-link i {
    font-size: var(--font-size-2xl);
    color: var(--primary-color);
}

.quick-link span {
    font-weight: 600;
}

/* Footer */
.main-footer {
    background: var(--dark-color);
    color: white;
    padding: var(--spacing-3xl) 0 var(--spacing-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.footer-section h4 {
    color: white;
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-lg);
}

.footer-section p {
    color: #bdc3c7;
    margin-bottom: var(--spacing-sm);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: var(--spacing-sm);
}

.footer-section ul li a {
    color: #bdc3c7;
    transition: var(--transition-fast);
}

.footer-section ul li a:hover {
    color: white;
}

.footer-bottom {
    text-align: center;
    padding-top: var(--spacing-lg);
    border-top: 1px solid #34495e;
    color: #bdc3c7;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 100%;
        right: 0;
        width: 100%;
        background: white;
        flex-direction: column;
        padding: var(--spacing-lg);
        box-shadow: var(--shadow-medium);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: var(--transition-normal);
    }

    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--spacing-xl);
    }

    .title-main {
        font-size: var(--font-size-3xl);
    }

    .title-sub {
        font-size: var(--font-size-xl);
    }

    .hero-subtitle {
        font-size: var(--font-size-lg);
    }

    .hero-buttons {
        justify-content: center;
    }

    .about-grid {
        grid-template-columns: 1fr;
    }

    .parts-grid {
        grid-template-columns: 1fr;
    }

    .quick-links {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--spacing-md);
    }

    .hero {
        padding: 100px 0 var(--spacing-2xl);
    }

    .title-main {
        font-size: var(--font-size-2xl);
    }

    .title-sub {
        font-size: var(--font-size-lg);
    }

    .btn {
        padding: var(--spacing-sm) var(--spacing-lg);
        font-size: var(--font-size-sm);
    }

    .quick-links {
        grid-template-columns: 1fr;
    }

    .parts-grid {
        gap: var(--spacing-lg);
    }

    .part-card {
        margin: 0 var(--spacing-sm);
    }
}

/* Print Styles */
@media print {
    .main-header,
    .nav-toggle,
    .hero-buttons,
    .quick-access,
    .main-footer {
        display: none;
    }

    .hero {
        padding: var(--spacing-lg) 0;
        background: white;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
    }

    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
    }

    .part-card {
        page-break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #000080;
        --secondary-color: #000000;
        --text-primary: #000000;
        --text-secondary: #333333;
        --border-color: #000000;
    }

    .btn-primary {
        background: #000080;
        border: 2px solid #000080;
    }

    .btn-secondary {
        background: white;
        color: #000080;
        border: 2px solid #000080;
    }
}

/* Focus styles for accessibility */
a:focus,
button:focus,
.btn:focus {
    outline: 3px solid var(--accent-color);
    outline-offset: 2px;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Selection styles */
::selection {
    background: var(--primary-color);
    color: white;
}

/* Search Overlay Styles */
.search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    z-index: 2000;
    display: none;
    align-items: flex-start;
    justify-content: center;
    padding-top: 10vh;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.search-overlay.active {
    opacity: 1;
}

.search-container {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-heavy);
    width: 90%;
    max-width: 600px;
    max-height: 70vh;
    overflow: hidden;
    transform: translateY(-20px);
    transition: transform 0.3s ease;
}

.search-overlay.active .search-container {
    transform: translateY(0);
}

.search-header {
    display: flex;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.search-header input {
    flex: 1;
    border: none;
    outline: none;
    font-size: var(--font-size-lg);
    font-family: var(--font-family);
    padding: var(--spacing-sm);
    background: transparent;
    color: var(--text-primary);
    direction: rtl;
    text-align: right;
}

.search-header input::placeholder {
    color: var(--text-secondary);
}

.search-close {
    background: none;
    border: none;
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
    margin-right: var(--spacing-md);
}

.search-close:hover {
    background: var(--light-color);
    color: var(--text-primary);
}

.search-results {
    max-height: 50vh;
    overflow-y: auto;
    padding: var(--spacing-lg);
}

.search-placeholder,
.search-no-results {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-secondary);
}

.search-placeholder i,
.search-no-results i {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
    opacity: 0.5;
}

.search-result-item {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition-fast);
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-item:hover {
    background: var(--light-color);
}

.search-result-item h4 {
    margin-bottom: var(--spacing-sm);
}

.search-result-item h4 a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
}

.search-result-item h4 a:hover {
    color: var(--accent-color);
}

.search-result-item p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin: 0;
}

/* Keyboard navigation styles */
.keyboard-navigation *:focus {
    outline: 3px solid var(--accent-color);
    outline-offset: 2px;
}

.skip-link:focus {
    top: 6px !important;
}

/* Quiz System Styles */
.quiz-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
    color: white;
    text-align: center;
}

.quiz-intro h4 {
    margin-bottom: var(--spacing-sm);
    font-size: 1.3rem;
}

.quiz-intro p {
    margin-bottom: var(--spacing-md);
    opacity: 0.9;
}

.quiz-start-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.quiz-start-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

/* Quiz Modal Styles */
.quiz-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: var(--spacing-md);
}

.quiz-modal-content {
    background: white;
    border-radius: 12px;
    width: 100%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.quiz-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid #eee;
    background: var(--primary-color);
    color: white;
    border-radius: 12px 12px 0 0;
}

.quiz-title {
    margin: 0;
    font-size: 1.2rem;
}

.quiz-close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 4px;
    transition: background 0.3s ease;
}

.quiz-close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.quiz-progress {
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid #eee;
}

.quiz-progress-bar {
    width: 100%;
    height: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
}

.quiz-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transition: width 0.3s ease;
}

.quiz-progress-text {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.quiz-content {
    padding: var(--spacing-lg);
}

.quiz-question h4 {
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
    font-size: 1.1rem;
    line-height: 1.6;
}

.quiz-options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.quiz-option {
    display: flex;
    align-items: center;
    padding: var(--spacing-md);
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fafafa;
}

.quiz-option:hover {
    border-color: var(--primary-color);
    background: #f0f8ff;
}

.quiz-option input[type="radio"] {
    margin-left: var(--spacing-sm);
    transform: scale(1.2);
}

.quiz-option input[type="radio"]:checked + .quiz-option-text {
    color: var(--primary-color);
    font-weight: 600;
}

.quiz-option:has(input:checked) {
    border-color: var(--primary-color);
    background: #f0f8ff;
}

.quiz-navigation {
    display: flex;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-top: 1px solid #eee;
    background: #fafafa;
    border-radius: 0 0 12px 12px;
}

.quiz-navigation .btn {
    min-width: 120px;
}

/* Quiz Results Styles */
.quiz-results {
    text-align: center;
}

.quiz-score {
    padding: var(--spacing-xl);
    border-radius: 12px;
    margin-bottom: var(--spacing-lg);
}

.quiz-results.excellent .quiz-score {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
}

.quiz-results.good .quiz-score {
    background: linear-gradient(135deg, #FF9800, #f57c00);
    color: white;
}

.quiz-results.needs-improvement .quiz-score {
    background: linear-gradient(135deg, #f44336, #d32f2f);
    color: white;
}

.quiz-score i {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
}

.quiz-score h3 {
    margin: var(--spacing-md) 0;
    font-size: 1.5rem;
}

.quiz-percentage {
    font-size: 3rem;
    font-weight: bold;
    margin: var(--spacing-md) 0;
}

.quiz-message {
    font-size: 1.1rem;
    margin: 0;
    opacity: 0.9;
}

.quiz-review {
    text-align: right;
}

.quiz-review h4 {
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: var(--spacing-sm);
}

.quiz-review-item {
    background: #f9f9f9;
    border-radius: 8px;
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    border-right: 4px solid #ddd;
}

.quiz-review-item.correct {
    border-right-color: #4CAF50;
    background: #f1f8e9;
}

.quiz-review-item.incorrect {
    border-right-color: #f44336;
    background: #ffebee;
}

.quiz-review-question {
    margin-bottom: var(--spacing-sm);
    font-weight: 600;
}

.quiz-review-answer,
.quiz-review-correct {
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.quiz-review-label {
    font-weight: 600;
    color: var(--text-secondary);
    min-width: 100px;
}

.quiz-review-explanation {
    background: rgba(255, 255, 255, 0.7);
    padding: var(--spacing-sm);
    border-radius: 4px;
    font-size: 0.9rem;
    line-height: 1.5;
}

.text-success {
    color: #4CAF50;
}

.text-danger {
    color: #f44336;
}

/* Responsive Quiz Styles */
@media (max-width: 768px) {
    .quiz-modal {
        padding: var(--spacing-sm);
    }

    .quiz-modal-content {
        max-height: 95vh;
    }

    .quiz-header {
        padding: var(--spacing-md);
    }

    .quiz-title {
        font-size: 1rem;
    }

    .quiz-content {
        padding: var(--spacing-md);
    }

    .quiz-navigation {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .quiz-navigation .btn {
        width: 100%;
    }

    .quiz-percentage {
        font-size: 2rem;
    }

    .quiz-review-answer,
    .quiz-review-correct {
        flex-direction: column;
        align-items: flex-start;
    }

    .quiz-review-label {
        min-width: auto;
    }
}

/* Risk Calculator Styles */
.risk-calculator-container {
    margin: var(--spacing-xl) 0;
}

.risk-calculator {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    padding: var(--spacing-xl);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-medium);
}

.calculator-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.calculator-header h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
    font-size: 1.4rem;
}

.calculator-header p {
    color: var(--text-secondary);
    margin: 0;
}

.calculator-inputs {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.input-group {
    display: flex;
    flex-direction: column;
}

.input-group label {
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.calculator-select {
    padding: var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    background: white;
    transition: border-color 0.3s ease;
    direction: rtl;
}

.calculator-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
}

.calculator-btn {
    grid-column: 1 / -1;
    justify-self: center;
    min-width: 200px;
}

.calculator-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Risk Result Styles */
.risk-result {
    margin: var(--spacing-xl) 0;
}

.risk-result-content {
    background: white;
    border-radius: 12px;
    padding: var(--spacing-xl);
    border-right: 4px solid;
    box-shadow: var(--shadow-light);
}

.risk-score {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    border-radius: 8px;
    margin-bottom: var(--spacing-lg);
    color: white;
}

.score-number {
    font-size: 2.5rem;
    font-weight: bold;
}

.score-level {
    font-size: 1.2rem;
    font-weight: 600;
}

.risk-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.risk-calculation,
.risk-action,
.risk-recommendations {
    background: #f8f9fa;
    padding: var(--spacing-md);
    border-radius: 8px;
}

.risk-calculation h6,
.risk-action h6,
.risk-recommendations h6 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
    font-size: 1rem;
}

.risk-recommendations ul {
    margin: 0;
    padding-right: var(--spacing-lg);
}

.risk-recommendations li {
    margin-bottom: var(--spacing-xs);
}

/* Risk Matrix Styles */
.risk-matrix-display {
    margin: var(--spacing-xl) 0;
}

.risk-matrix-display h5 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
}

.matrix-table {
    overflow-x: auto;
}

.risk-matrix-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

.risk-matrix-table th,
.risk-matrix-table td {
    padding: var(--spacing-md);
    text-align: center;
    border: 1px solid #ddd;
    font-weight: 600;
}

.risk-matrix-table th {
    background: var(--primary-color);
    color: white;
    font-size: 0.9rem;
}

.severity-label {
    background: var(--light-color);
    color: var(--text-primary);
    font-weight: 600;
}

.risk-cell {
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.risk-cell:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    z-index: 10;
}

.risk-cell.highlighted {
    animation: pulse 1s infinite;
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.8);
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Examples Section */
.risk-examples {
    margin-top: var(--spacing-xl);
}

.risk-examples h5 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
}

.examples-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.example-card {
    background: white;
    padding: var(--spacing-lg);
    border-radius: 8px;
    text-align: center;
    box-shadow: var(--shadow-light);
    transition: transform 0.3s ease;
}

.example-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.example-card h6 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
    font-size: 1rem;
}

.example-card p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: var(--spacing-md);
}

.example-btn {
    font-size: 0.85rem;
    padding: var(--spacing-sm) var(--spacing-md);
}

/* Responsive Risk Calculator */
@media (max-width: 768px) {
    .calculator-inputs {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .risk-details {
        grid-template-columns: 1fr;
    }

    .examples-grid {
        grid-template-columns: 1fr;
    }

    .risk-matrix-table {
        font-size: 0.8rem;
    }

    .risk-matrix-table th,
    .risk-matrix-table td {
        padding: var(--spacing-sm);
    }

    .score-number {
        font-size: 2rem;
    }

    .score-level {
        font-size: 1rem;
    }
}

/* Interactive Checklists Styles */
.interactive-checklist-container {
    margin: var(--spacing-xl) 0;
}

.interactive-checklist {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    padding: var(--spacing-xl);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-medium);
}

.checklist-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.checklist-header h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
    font-size: 1.4rem;
}

.checklist-header p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
}

.checklist-progress {
    max-width: 400px;
    margin: 0 auto;
}

.progress-bar {
    width: 100%;
    height: 12px;
    background: #e0e0e0;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--success-color), #27ae60);
    transition: width 0.5s ease;
    border-radius: 6px;
}

.progress-text {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 600;
}

.checklist-controls {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
    flex-wrap: wrap;
}

.checklist-controls .btn {
    font-size: 0.9rem;
    padding: var(--spacing-sm) var(--spacing-md);
}

/* Category Styles */
.checklist-category {
    background: white;
    border-radius: 8px;
    margin-bottom: var(--spacing-md);
    box-shadow: var(--shadow-light);
    overflow: hidden;
    transition: all 0.3s ease;
}

.checklist-category.completed {
    border-right: 4px solid var(--success-color);
}

.category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    background: #f8f9fa;
    cursor: pointer;
    transition: background 0.3s ease;
}

.category-header:hover {
    background: #e9ecef;
}

.category-header h5 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.category-progress {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: normal;
}

.category-toggle {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.2rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 4px;
    transition: all 0.3s ease;
}

.category-toggle:hover {
    background: rgba(0, 0, 0, 0.1);
    color: var(--primary-color);
}

.category-items {
    padding: var(--spacing-md);
}

/* Checklist Item Styles */
.checklist-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: 6px;
    margin-bottom: var(--spacing-sm);
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.checklist-item:hover {
    background: #f0f8ff;
    border-color: var(--primary-color);
}

.checklist-item.critical {
    border-right: 3px solid var(--danger-color);
    background: #fff5f5;
}

.item-checkbox {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    cursor: pointer;
    flex: 1;
    font-size: 0.95rem;
    line-height: 1.5;
}

.item-checkbox input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    flex-shrink: 0;
    margin-top: 2px;
}

.item-checkbox input[type="checkbox"]:checked + .checkmark {
    background: var(--success-color);
    border-color: var(--success-color);
}

.item-checkbox input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    color: white;
    font-weight: bold;
    font-size: 14px;
}

.item-text {
    color: var(--text-primary);
    transition: color 0.3s ease;
}

.item-checkbox input[type="checkbox"]:checked + .checkmark + .item-text {
    color: var(--text-secondary);
    text-decoration: line-through;
}

.critical-badge {
    background: var(--danger-color);
    color: white;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 600;
    margin-right: var(--spacing-sm);
}

.item-actions {
    display: flex;
    gap: var(--spacing-xs);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.checklist-item:hover .item-actions {
    opacity: 1;
}

.item-note-btn,
.item-photo-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 0.9rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 4px;
    transition: all 0.3s ease;
}

.item-note-btn:hover,
.item-photo-btn:hover {
    background: var(--light-color);
    color: var(--primary-color);
}

.item-note-btn.has-note {
    color: var(--warning-color);
    background: rgba(243, 156, 18, 0.1);
}

.item-note {
    width: 100%;
    margin-top: var(--spacing-sm);
    padding: var(--spacing-sm);
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
}

.item-note textarea {
    width: 100%;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: var(--spacing-sm);
    font-family: var(--font-family);
    font-size: 0.9rem;
    resize: vertical;
    margin-bottom: var(--spacing-sm);
    direction: rtl;
}

.item-note textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.save-note-btn {
    font-size: 0.8rem;
    padding: var(--spacing-xs) var(--spacing-sm);
}

/* Summary Styles */
.checklist-summary {
    background: white;
    border-radius: 8px;
    padding: var(--spacing-lg);
    margin-top: var(--spacing-xl);
    box-shadow: var(--shadow-light);
}

.checklist-summary h5 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
    text-align: center;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 600;
}

/* Responsive Checklist Styles */
@media (max-width: 768px) {
    .checklist-controls {
        flex-direction: column;
        align-items: center;
    }

    .checklist-controls .btn {
        width: 100%;
        max-width: 200px;
    }

    .checklist-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .item-actions {
        opacity: 1;
        margin-top: var(--spacing-sm);
    }

    .summary-stats {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .category-header {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .category-header h5 {
        font-size: 1rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }
}

/* Glossary System Styles */
.glossary-toggle-btn {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50px;
    padding: var(--spacing-md) var(--spacing-lg);
    box-shadow: var(--shadow-medium);
    cursor: pointer;
    z-index: 999;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
}

.glossary-toggle-btn:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-large);
}

.glossary-toggle-btn i {
    font-size: 1.2rem;
}

/* Glossary Modal */
.glossary-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: var(--spacing-md);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.glossary-modal.active {
    opacity: 1;
}

.glossary-modal-content {
    background: white;
    border-radius: 12px;
    width: 100%;
    max-width: 900px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
}

.glossary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    background: var(--primary-color);
    color: white;
}

.glossary-header h3 {
    margin: 0;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.glossary-close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 4px;
    transition: background 0.3s ease;
}

.glossary-close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Search and Filters */
.glossary-search {
    padding: var(--spacing-lg);
    border-bottom: 1px solid #eee;
    background: #f8f9fa;
}

.search-input-container {
    position: relative;
    margin-bottom: var(--spacing-md);
}

.search-input-container i {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

#glossary-search {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-xl) var(--spacing-md) var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    direction: rtl;
    transition: border-color 0.3s ease;
}

#glossary-search:focus {
    outline: none;
    border-color: var(--primary-color);
}

.glossary-filters {
    display: flex;
    gap: var(--spacing-md);
}

#category-filter {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: 6px;
    font-family: var(--font-family);
    background: white;
    direction: rtl;
}

/* Glossary Content */
.glossary-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-lg);
}

.glossary-stats {
    text-align: center;
    margin-bottom: var(--spacing-lg);
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.glossary-category {
    margin-bottom: var(--spacing-xl);
}

.category-title {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: var(--spacing-sm);
}

.category-count {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: normal;
}

.category-items {
    display: grid;
    gap: var(--spacing-md);
}

/* Glossary Items */
.glossary-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: var(--spacing-lg);
    border-right: 4px solid var(--primary-color);
    transition: all 0.3s ease;
    position: relative;
}

.glossary-item:hover {
    background: #e9ecef;
    transform: translateY(-1px);
    box-shadow: var(--shadow-light);
}

.term-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.term-name {
    color: var(--primary-color);
    margin: 0;
    font-size: 1.1rem;
    font-weight: 700;
}

.term-arabic {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 600;
}

.term-full-name {
    color: var(--text-secondary);
    font-size: 0.85rem;
    margin-bottom: var(--spacing-sm);
    font-style: italic;
}

.term-definition {
    color: var(--text-primary);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
}

.term-actions {
    display: flex;
    gap: var(--spacing-sm);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.glossary-item:hover .term-actions {
    opacity: 1;
}

.copy-term-btn,
.highlight-term-btn {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.copy-term-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.highlight-term-btn:hover {
    background: var(--warning-color);
    color: white;
    border-color: var(--warning-color);
}

/* Terms in Content */
.glossary-term {
    background: linear-gradient(120deg, #a8e6cf 0%, #dcedc1 100%);
    padding: 2px 4px;
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 1px dotted var(--primary-color);
}

.glossary-term:hover {
    background: linear-gradient(120deg, #81c784 0%, #aed581 100%);
    transform: scale(1.05);
}

.highlighted-term {
    background: linear-gradient(120deg, #ffeb3b 0%, #ffc107 100%) !important;
    animation: highlight-pulse 2s ease-in-out;
}

@keyframes highlight-pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Term Popup */
.term-popup {
    position: absolute;
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    z-index: 1001;
    max-width: 350px;
    border: 1px solid var(--border-color);
}

.popup-content {
    padding: var(--spacing-md);
}

.popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
    font-size: 1.1rem;
}

.popup-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.popup-arabic {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: var(--spacing-sm);
    font-weight: 600;
}

.popup-definition {
    color: var(--text-primary);
    line-height: 1.5;
    margin-bottom: var(--spacing-md);
    font-size: 0.9rem;
}

.popup-actions {
    text-align: center;
}

.open-glossary-btn {
    font-size: 0.8rem;
    padding: var(--spacing-xs) var(--spacing-sm);
}

/* No Results */
.no-results {
    text-align: center;
    color: var(--text-secondary);
    padding: var(--spacing-xl);
}

.no-results i {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

/* Notification */
.glossary-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--success-color);
    color: white;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: 8px;
    box-shadow: var(--shadow-medium);
    z-index: 1002;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    font-weight: 600;
}

.glossary-notification.show {
    transform: translateX(0);
}

/* Responsive Glossary */
@media (max-width: 768px) {
    .glossary-toggle-btn {
        bottom: 15px;
        left: 15px;
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .glossary-toggle-btn span {
        display: none;
    }

    .glossary-modal {
        padding: var(--spacing-sm);
    }

    .glossary-modal-content {
        max-height: 95vh;
    }

    .glossary-header {
        padding: var(--spacing-md);
    }

    .glossary-header h3 {
        font-size: 1.1rem;
    }

    .glossary-search {
        padding: var(--spacing-md);
    }

    .glossary-content {
        padding: var(--spacing-md);
    }

    .term-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .term-actions {
        opacity: 1;
        margin-top: var(--spacing-sm);
    }

    .term-popup {
        max-width: 280px;
        left: 10px !important;
        right: 10px !important;
        width: auto !important;
    }

    .glossary-notification {
        right: 10px;
        left: 10px;
        text-align: center;
    }
}

/* Case Studies System Styles */
.case-study-container {
    margin: var(--spacing-xl) 0;
}

.case-study-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: var(--spacing-xl);
    color: white;
    text-align: center;
    box-shadow: var(--shadow-medium);
}

.case-study-header h3 {
    margin-bottom: var(--spacing-md);
    font-size: 1.4rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.case-study-description {
    margin-bottom: var(--spacing-lg);
    opacity: 0.9;
    font-size: 1.1rem;
    line-height: 1.6;
}

.case-study-start-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: 8px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.case-study-start-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

/* Case Study Modal */
.case-study-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: var(--spacing-md);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.case-study-modal.active {
    opacity: 1;
    visibility: visible;
}

.case-study-modal-content {
    background: white;
    border-radius: 12px;
    width: 100%;
    max-width: 900px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.case-study-modal.active .case-study-modal-content {
    transform: scale(1);
}

.case-study-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    background: var(--primary-color);
    color: white;
}

.case-study-modal-header h3 {
    margin: 0;
    font-size: 1.3rem;
}

.case-study-close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 4px;
    transition: background 0.3s ease;
}

.case-study-close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Progress Steps */
.case-study-progress {
    padding: var(--spacing-lg);
    background: #f8f9fa;
    border-bottom: 1px solid #eee;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.progress-steps::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background: #e0e0e0;
    z-index: 1;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 2;
    background: #f8f9fa;
    padding: var(--spacing-xs);
    border-radius: 8px;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e0e0e0;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: var(--spacing-xs);
    transition: all 0.3s ease;
}

.step-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    text-align: center;
    font-weight: 600;
}

.step.active .step-number {
    background: var(--primary-color);
    color: white;
}

.step.active .step-label {
    color: var(--primary-color);
}

.step.completed .step-number {
    background: var(--success-color);
    color: white;
}

.step.completed .step-number::after {
    content: '✓';
    font-size: 0.8rem;
}

/* Case Study Content */
.case-study-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-xl);
}

.case-study-step h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: var(--spacing-sm);
}

.scenario-content p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-primary);
    background: #f8f9fa;
    padding: var(--spacing-lg);
    border-radius: 8px;
    border-right: 4px solid var(--primary-color);
}

/* Challenges List */
.challenges-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.challenge-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: #fff5f5;
    border-radius: 8px;
    border-right: 4px solid var(--danger-color);
}

.challenge-number {
    background: var(--danger-color);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.challenge-text {
    color: var(--text-primary);
    line-height: 1.6;
}

/* Solutions List */
.solutions-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.solution-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: #f0f8ff;
    border-radius: 8px;
    border-right: 4px solid var(--primary-color);
}

.solution-number {
    background: var(--primary-color);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.solution-text {
    color: var(--text-primary);
    line-height: 1.6;
}

/* Outcomes List */
.outcomes-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.outcome-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: #f1f8e9;
    border-radius: 8px;
    border-right: 4px solid var(--success-color);
}

.outcome-item i {
    color: var(--success-color);
    font-size: 1.2rem;
    margin-top: 2px;
}

.outcome-text {
    color: var(--text-primary);
    line-height: 1.6;
    font-weight: 600;
}

/* Lessons List */
.lessons-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.lesson-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: #fff8e1;
    border-radius: 8px;
    border-right: 4px solid var(--warning-color);
}

.lesson-number {
    background: var(--warning-color);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.lesson-text {
    color: var(--text-primary);
    line-height: 1.6;
    font-weight: 500;
}

/* Case Study Completion */
.case-study-completion {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    padding: var(--spacing-xl);
    border-radius: 12px;
    text-align: center;
    margin-top: var(--spacing-lg);
}

.completion-message i {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
}

.completion-message h5 {
    margin-bottom: var(--spacing-sm);
    font-size: 1.3rem;
}

.case-study-completion-full {
    text-align: center;
    padding: var(--spacing-xl);
}

.completion-animation {
    margin-bottom: var(--spacing-xl);
}

.completion-animation i {
    font-size: 5rem;
    color: #FFD700;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-20px); }
    60% { transform: translateY(-10px); }
}

.case-study-completion-full h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    font-size: 1.5rem;
}

.case-study-completion-full p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    font-size: 1.1rem;
}

.completion-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

/* Case Study Navigation */
.case-study-navigation {
    display: flex;
    justify-content: space-between;
    padding: var(--spacing-lg);
    background: #f8f9fa;
    border-top: 1px solid #eee;
}

.case-study-navigation .btn {
    min-width: 120px;
}

/* Responsive Case Studies */
@media (max-width: 768px) {
    .case-study-modal {
        padding: var(--spacing-sm);
    }

    .case-study-modal-content {
        max-height: 95vh;
    }

    .case-study-modal-header {
        padding: var(--spacing-md);
    }

    .case-study-modal-header h3 {
        font-size: 1.1rem;
    }

    .case-study-progress {
        padding: var(--spacing-md);
    }

    .progress-steps {
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }

    .step {
        flex: 1;
        min-width: 80px;
    }

    .step-number {
        width: 30px;
        height: 30px;
        font-size: 0.8rem;
    }

    .step-label {
        font-size: 0.7rem;
    }

    .case-study-content {
        padding: var(--spacing-md);
    }

    .case-study-step h4 {
        font-size: 1.1rem;
    }

    .challenge-item,
    .solution-item,
    .outcome-item,
    .lesson-item {
        flex-direction: column;
        text-align: center;
    }

    .challenge-number,
    .solution-number,
    .lesson-number {
        align-self: center;
    }

    .case-study-navigation {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .case-study-navigation .btn {
        width: 100%;
    }

    .completion-actions {
        flex-direction: column;
    }

    .completion-actions .btn {
        width: 100%;
    }
}
