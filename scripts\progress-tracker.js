/**
 * نظام تتبع التقدم الشامل
 * Comprehensive Progress Tracking System
 */

class ProgressTracker {
    constructor() {
        this.storageKey = 'aami_guide_progress';
        this.userProgress = this.loadProgress();
        this.courseStructure = {
            'part1': {
                title: 'الجزء الأول: الأسس والمبادئ',
                chapters: {
                    'chapter1': { title: 'مقدمة في معايير AAMI', weight: 25 },
                    'chapter2': { title: 'دور مهندس الطب الحيوي', weight: 25 },
                    'chapter3': { title: 'نظرة شاملة على معايير AAMI', weight: 50 }
                }
            },
            'part2': {
                title: 'الجزء الثاني: التطبيقات العملية',
                chapters: {
                    'chapter4': { title: 'دورة حياة الأجهزة الطبية', weight: 25 },
                    'chapter5': { title: 'إدارة المخاطر', weight: 25 },
                    'chapter6': { title: 'الصيانة الوقائية', weight: 25 },
                    'chapter7': { title: 'ضمان الجودة والامتثال', weight: 25 }
                }
            },
            'part3': {
                title: 'الجزء الثالث: المواضيع المتقدمة',
                chapters: {
                    'chapter8': { title: 'الأمن السيبراني', weight: 33 },
                    'chapter9': { title: 'التشغيل البيني', weight: 33 },
                    'chapter10': { title: 'التحضير للتدقيق', weight: 34 }
                }
            }
        };
        
        this.init();
    }

    init() {
        this.createProgressInterface();
        this.trackPageVisits();
        this.trackInteractiveElements();
        this.updateProgressDisplay();
        this.setupEventListeners();
    }

    loadProgress() {
        try {
            const saved = localStorage.getItem(this.storageKey);
            return saved ? JSON.parse(saved) : this.getDefaultProgress();
        } catch (error) {
            console.warn('خطأ في تحميل بيانات التقدم:', error);
            return this.getDefaultProgress();
        }
    }

    getDefaultProgress() {
        return {
            user: {
                name: '',
                startDate: new Date().toISOString(),
                lastVisit: new Date().toISOString(),
                totalTimeSpent: 0
            },
            pages: {},
            quizzes: {},
            checklists: {},
            achievements: [],
            bookmarks: [],
            notes: {},
            overallProgress: 0,
            partProgress: {
                part1: 0,
                part2: 0,
                part3: 0
            }
        };
    }

    saveProgress() {
        try {
            this.userProgress.user.lastVisit = new Date().toISOString();
            localStorage.setItem(this.storageKey, JSON.stringify(this.userProgress));
        } catch (error) {
            console.warn('خطأ في حفظ بيانات التقدم:', error);
        }
    }

    createProgressInterface() {
        // إنشاء زر لوحة التقدم
        const progressButton = document.createElement('button');
        progressButton.className = 'progress-tracker-btn';
        progressButton.innerHTML = `
            <i class="fas fa-chart-line"></i>
            <span>تتبع التقدم</span>
            <div class="progress-indicator">
                <div class="progress-circle">
                    <span class="progress-percentage">0%</span>
                </div>
            </div>
        `;
        progressButton.title = 'عرض تقدمك في الدليل';
        
        document.body.appendChild(progressButton);
        
        // إنشاء لوحة التقدم
        this.createProgressDashboard();
    }

    createProgressDashboard() {
        const dashboard = document.createElement('div');
        dashboard.className = 'progress-dashboard';
        dashboard.innerHTML = `
            <div class="progress-dashboard-content">
                <div class="dashboard-header">
                    <h3>
                        <i class="fas fa-chart-line"></i>
                        لوحة تتبع التقدم
                    </h3>
                    <button class="dashboard-close-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="dashboard-tabs">
                    <button class="tab-btn active" data-tab="overview">نظرة عامة</button>
                    <button class="tab-btn" data-tab="parts">الأجزاء</button>
                    <button class="tab-btn" data-tab="achievements">الإنجازات</button>
                    <button class="tab-btn" data-tab="stats">الإحصائيات</button>
                </div>
                
                <div class="dashboard-content">
                    <div class="tab-content active" id="overview-tab">
                        ${this.createOverviewTab()}
                    </div>
                    <div class="tab-content" id="parts-tab">
                        ${this.createPartsTab()}
                    </div>
                    <div class="tab-content" id="achievements-tab">
                        ${this.createAchievementsTab()}
                    </div>
                    <div class="tab-content" id="stats-tab">
                        ${this.createStatsTab()}
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(dashboard);
    }

    createOverviewTab() {
        const overallProgress = this.calculateOverallProgress();
        const timeSpent = this.formatTime(this.userProgress.user.totalTimeSpent);
        const startDate = new Date(this.userProgress.user.startDate).toLocaleDateString('ar-SA');
        
        return `
            <div class="overview-content">
                <div class="progress-summary">
                    <div class="main-progress">
                        <div class="progress-circle-large">
                            <svg viewBox="0 0 100 100">
                                <circle cx="50" cy="50" r="45" fill="none" stroke="#e0e0e0" stroke-width="8"/>
                                <circle cx="50" cy="50" r="45" fill="none" stroke="var(--primary-color)" 
                                        stroke-width="8" stroke-dasharray="283" 
                                        stroke-dashoffset="${283 - (283 * overallProgress / 100)}"
                                        transform="rotate(-90 50 50)"/>
                            </svg>
                            <div class="progress-text">
                                <span class="percentage">${overallProgress}%</span>
                                <span class="label">مكتمل</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="progress-stats">
                        <div class="stat-item">
                            <i class="fas fa-calendar-alt"></i>
                            <div class="stat-content">
                                <span class="stat-value">${startDate}</span>
                                <span class="stat-label">تاريخ البداية</span>
                            </div>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-clock"></i>
                            <div class="stat-content">
                                <span class="stat-value">${timeSpent}</span>
                                <span class="stat-label">الوقت المستغرق</span>
                            </div>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-trophy"></i>
                            <div class="stat-content">
                                <span class="stat-value">${this.userProgress.achievements.length}</span>
                                <span class="stat-label">الإنجازات</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="recent-activity">
                    <h4>النشاط الأخير</h4>
                    <div class="activity-list">
                        ${this.getRecentActivity()}
                    </div>
                </div>
                
                <div class="next-steps">
                    <h4>الخطوات التالية</h4>
                    <div class="recommendations">
                        ${this.getRecommendations()}
                    </div>
                </div>
            </div>
        `;
    }

    createPartsTab() {
        let html = '<div class="parts-progress">';
        
        Object.entries(this.courseStructure).forEach(([partId, part]) => {
            const partProgress = this.calculatePartProgress(partId);
            html += `
                <div class="part-card">
                    <div class="part-header">
                        <h4>${part.title}</h4>
                        <div class="part-progress-bar">
                            <div class="progress-fill" style="width: ${partProgress}%"></div>
                        </div>
                        <span class="part-percentage">${partProgress}%</span>
                    </div>
                    <div class="chapters-list">
                        ${Object.entries(part.chapters).map(([chapterId, chapter]) => {
                            const chapterProgress = this.getChapterProgress(partId, chapterId);
                            return `
                                <div class="chapter-item ${chapterProgress.completed ? 'completed' : ''}">
                                    <div class="chapter-info">
                                        <span class="chapter-title">${chapter.title}</span>
                                        <div class="chapter-indicators">
                                            ${chapterProgress.visited ? '<i class="fas fa-eye" title="تم زيارته"></i>' : ''}
                                            ${chapterProgress.quizCompleted ? '<i class="fas fa-check-circle" title="اختبار مكتمل"></i>' : ''}
                                            ${chapterProgress.checklistCompleted ? '<i class="fas fa-list-check" title="قائمة فحص مكتملة"></i>' : ''}
                                        </div>
                                    </div>
                                    <div class="chapter-progress">${chapterProgress.progress}%</div>
                                </div>
                            `;
                        }).join('')}
                    </div>
                </div>
            `;
        });
        
        html += '</div>';
        return html;
    }

    createAchievementsTab() {
        const allAchievements = this.getAllAchievements();
        const unlockedAchievements = this.userProgress.achievements;
        
        return `
            <div class="achievements-content">
                <div class="achievements-summary">
                    <h4>الإنجازات المحققة: ${unlockedAchievements.length}/${allAchievements.length}</h4>
                    <div class="achievements-progress-bar">
                        <div class="progress-fill" style="width: ${(unlockedAchievements.length / allAchievements.length) * 100}%"></div>
                    </div>
                </div>
                
                <div class="achievements-grid">
                    ${allAchievements.map(achievement => {
                        const isUnlocked = unlockedAchievements.some(a => a.id === achievement.id);
                        return `
                            <div class="achievement-card ${isUnlocked ? 'unlocked' : 'locked'}">
                                <div class="achievement-icon">
                                    <i class="${achievement.icon}"></i>
                                </div>
                                <div class="achievement-info">
                                    <h5>${achievement.title}</h5>
                                    <p>${achievement.description}</p>
                                    ${isUnlocked ? `<span class="unlock-date">تم الإنجاز: ${new Date(unlockedAchievements.find(a => a.id === achievement.id).date).toLocaleDateString('ar-SA')}</span>` : ''}
                                </div>
                            </div>
                        `;
                    }).join('')}
                </div>
            </div>
        `;
    }

    createStatsTab() {
        const stats = this.calculateDetailedStats();
        
        return `
            <div class="stats-content">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-book-open"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-number">${stats.pagesVisited}</span>
                            <span class="stat-label">صفحة مزارة</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-question-circle"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-number">${stats.quizzesCompleted}</span>
                            <span class="stat-label">اختبار مكتمل</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-list-check"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-number">${stats.checklistsCompleted}</span>
                            <span class="stat-label">قائمة فحص مكتملة</span>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-bookmark"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-number">${stats.bookmarks}</span>
                            <span class="stat-label">إشارة مرجعية</span>
                        </div>
                    </div>
                </div>
                
                <div class="detailed-stats">
                    <h4>إحصائيات مفصلة</h4>
                    <div class="stats-table">
                        <div class="stats-row">
                            <span class="stats-label">متوسط نتائج الاختبارات:</span>
                            <span class="stats-value">${stats.averageQuizScore}%</span>
                        </div>
                        <div class="stats-row">
                            <span class="stats-label">أفضل نتيجة اختبار:</span>
                            <span class="stats-value">${stats.bestQuizScore}%</span>
                        </div>
                        <div class="stats-row">
                            <span class="stats-label">عدد الزيارات:</span>
                            <span class="stats-value">${stats.totalVisits}</span>
                        </div>
                        <div class="stats-row">
                            <span class="stats-label">متوسط الوقت في الصفحة:</span>
                            <span class="stats-value">${this.formatTime(stats.averageTimePerPage)}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    setupEventListeners() {
        // فتح/إغلاق لوحة التقدم
        document.addEventListener('click', (e) => {
            if (e.target.closest('.progress-tracker-btn')) {
                this.toggleDashboard();
            }
            
            if (e.target.closest('.dashboard-close-btn')) {
                this.closeDashboard();
            }
            
            // تبديل التبويبات
            if (e.target.classList.contains('tab-btn')) {
                this.switchTab(e.target.dataset.tab);
            }
        });

        // إغلاق عند النقر خارج اللوحة
        document.addEventListener('click', (e) => {
            const dashboard = document.querySelector('.progress-dashboard');
            if (e.target === dashboard) {
                this.closeDashboard();
            }
        });

        // تتبع الوقت المستغرق
        this.startTimeTracking();
    }

    trackPageVisits() {
        const currentPage = this.getCurrentPageId();
        if (currentPage) {
            if (!this.userProgress.pages[currentPage]) {
                this.userProgress.pages[currentPage] = {
                    firstVisit: new Date().toISOString(),
                    visits: 0,
                    timeSpent: 0,
                    completed: false
                };
            }
            
            this.userProgress.pages[currentPage].visits++;
            this.userProgress.pages[currentPage].lastVisit = new Date().toISOString();
            this.saveProgress();
        }
    }

    trackInteractiveElements() {
        // تتبع إكمال الاختبارات
        document.addEventListener('quiz-completed', (e) => {
            const { quizId, score, totalQuestions } = e.detail;
            this.userProgress.quizzes[quizId] = {
                completed: true,
                score: score,
                totalQuestions: totalQuestions,
                percentage: Math.round((score / totalQuestions) * 100),
                date: new Date().toISOString()
            };
            
            this.checkAchievements();
            this.updateProgressDisplay();
            this.saveProgress();
        });

        // تتبع إكمال قوائم الفحص
        document.addEventListener('checklist-completed', (e) => {
            const { checklistId, completedItems, totalItems } = e.detail;
            this.userProgress.checklists[checklistId] = {
                completed: true,
                completedItems: completedItems,
                totalItems: totalItems,
                percentage: Math.round((completedItems / totalItems) * 100),
                date: new Date().toISOString()
            };
            
            this.checkAchievements();
            this.updateProgressDisplay();
            this.saveProgress();
        });
    }

    getCurrentPageId() {
        const path = window.location.pathname;
        const match = path.match(/\/(part\d+)\/(chapter\d+)\.html/);
        return match ? `${match[1]}_${match[2]}` : null;
    }

    calculateOverallProgress() {
        const totalParts = Object.keys(this.courseStructure).length;
        let totalProgress = 0;
        
        Object.keys(this.courseStructure).forEach(partId => {
            totalProgress += this.calculatePartProgress(partId);
        });
        
        return Math.round(totalProgress / totalParts);
    }

    calculatePartProgress(partId) {
        const part = this.courseStructure[partId];
        if (!part) return 0;
        
        const chapters = Object.keys(part.chapters);
        let totalProgress = 0;
        
        chapters.forEach(chapterId => {
            const chapterProgress = this.getChapterProgress(partId, chapterId);
            totalProgress += chapterProgress.progress;
        });
        
        return Math.round(totalProgress / chapters.length);
    }

    getChapterProgress(partId, chapterId) {
        const pageId = `${partId}_${chapterId}`;
        const pageData = this.userProgress.pages[pageId];
        const quizData = this.userProgress.quizzes[pageId];
        const checklistData = this.userProgress.checklists[pageId];
        
        let progress = 0;
        let visited = false;
        let quizCompleted = false;
        let checklistCompleted = false;
        
        if (pageData && pageData.visits > 0) {
            visited = true;
            progress += 40; // 40% للزيارة
        }
        
        if (quizData && quizData.completed) {
            quizCompleted = true;
            progress += 30; // 30% للاختبار
        }
        
        if (checklistData && checklistData.completed) {
            checklistCompleted = true;
            progress += 30; // 30% لقائمة الفحص
        }
        
        return {
            progress: Math.min(progress, 100),
            visited,
            quizCompleted,
            checklistCompleted,
            completed: progress >= 70 // يعتبر مكتملاً عند 70%
        };
    }

    getAllAchievements() {
        return [
            {
                id: 'first_visit',
                title: 'أول زيارة',
                description: 'مرحباً بك في دليل معايير AAMI',
                icon: 'fas fa-door-open'
            },
            {
                id: 'first_quiz',
                title: 'أول اختبار',
                description: 'أكمل أول اختبار بنجاح',
                icon: 'fas fa-graduation-cap'
            },
            {
                id: 'quiz_master',
                title: 'خبير الاختبارات',
                description: 'احصل على 90% أو أكثر في اختبار',
                icon: 'fas fa-medal'
            },
            {
                id: 'part1_complete',
                title: 'إتمام الجزء الأول',
                description: 'أكمل جميع فصول الجزء الأول',
                icon: 'fas fa-check-circle'
            },
            {
                id: 'part2_complete',
                title: 'إتمام الجزء الثاني',
                description: 'أكمل جميع فصول الجزء الثاني',
                icon: 'fas fa-check-circle'
            },
            {
                id: 'part3_complete',
                title: 'إتمام الجزء الثالث',
                description: 'أكمل جميع فصول الجزء الثالث',
                icon: 'fas fa-check-circle'
            },
            {
                id: 'course_complete',
                title: 'إتمام الدليل',
                description: 'أكمل الدليل بالكامل',
                icon: 'fas fa-trophy'
            },
            {
                id: 'dedicated_learner',
                title: 'متعلم مثابر',
                description: 'اقض أكثر من 5 ساعات في التعلم',
                icon: 'fas fa-clock'
            }
        ];
    }

    checkAchievements() {
        const achievements = this.getAllAchievements();
        const currentAchievements = this.userProgress.achievements.map(a => a.id);
        
        achievements.forEach(achievement => {
            if (!currentAchievements.includes(achievement.id)) {
                if (this.isAchievementUnlocked(achievement.id)) {
                    this.unlockAchievement(achievement);
                }
            }
        });
    }

    isAchievementUnlocked(achievementId) {
        switch (achievementId) {
            case 'first_visit':
                return Object.keys(this.userProgress.pages).length > 0;
            
            case 'first_quiz':
                return Object.keys(this.userProgress.quizzes).length > 0;
            
            case 'quiz_master':
                return Object.values(this.userProgress.quizzes).some(quiz => quiz.percentage >= 90);
            
            case 'part1_complete':
                return this.calculatePartProgress('part1') >= 70;
            
            case 'part2_complete':
                return this.calculatePartProgress('part2') >= 70;
            
            case 'part3_complete':
                return this.calculatePartProgress('part3') >= 70;
            
            case 'course_complete':
                return this.calculateOverallProgress() >= 70;
            
            case 'dedicated_learner':
                return this.userProgress.user.totalTimeSpent >= 5 * 60 * 60 * 1000; // 5 ساعات
            
            default:
                return false;
        }
    }

    unlockAchievement(achievement) {
        this.userProgress.achievements.push({
            id: achievement.id,
            title: achievement.title,
            description: achievement.description,
            icon: achievement.icon,
            date: new Date().toISOString()
        });
        
        this.showAchievementNotification(achievement);
        this.saveProgress();
    }

    showAchievementNotification(achievement) {
        const notification = document.createElement('div');
        notification.className = 'achievement-notification';
        notification.innerHTML = `
            <div class="achievement-content">
                <div class="achievement-icon">
                    <i class="${achievement.icon}"></i>
                </div>
                <div class="achievement-text">
                    <h4>إنجاز جديد!</h4>
                    <h5>${achievement.title}</h5>
                    <p>${achievement.description}</p>
                </div>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => notification.classList.add('show'), 100);
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 500);
        }, 5000);
    }

    updateProgressDisplay() {
        const progressBtn = document.querySelector('.progress-tracker-btn');
        if (progressBtn) {
            const overallProgress = this.calculateOverallProgress();
            const progressPercentage = progressBtn.querySelector('.progress-percentage');
            if (progressPercentage) {
                progressPercentage.textContent = `${overallProgress}%`;
            }
        }
    }

    toggleDashboard() {
        const dashboard = document.querySelector('.progress-dashboard');
        if (dashboard.style.display === 'flex') {
            this.closeDashboard();
        } else {
            this.openDashboard();
        }
    }

    openDashboard() {
        const dashboard = document.querySelector('.progress-dashboard');
        dashboard.style.display = 'flex';
        setTimeout(() => dashboard.classList.add('active'), 10);
        
        // تحديث المحتوى
        this.refreshDashboardContent();
    }

    closeDashboard() {
        const dashboard = document.querySelector('.progress-dashboard');
        dashboard.classList.remove('active');
        setTimeout(() => dashboard.style.display = 'none', 300);
    }

    switchTab(tabName) {
        // إزالة الفئة النشطة من جميع التبويبات
        document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
        
        // تفعيل التبويب المحدد
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        document.querySelector(`#${tabName}-tab`).classList.add('active');
    }

    refreshDashboardContent() {
        // تحديث محتوى التبويبات
        document.getElementById('overview-tab').innerHTML = this.createOverviewTab();
        document.getElementById('parts-tab').innerHTML = this.createPartsTab();
        document.getElementById('achievements-tab').innerHTML = this.createAchievementsTab();
        document.getElementById('stats-tab').innerHTML = this.createStatsTab();
    }

    startTimeTracking() {
        let startTime = Date.now();
        
        // حفظ الوقت عند إغلاق الصفحة
        window.addEventListener('beforeunload', () => {
            const timeSpent = Date.now() - startTime;
            this.userProgress.user.totalTimeSpent += timeSpent;
            this.saveProgress();
        });
        
        // حفظ الوقت كل دقيقة
        setInterval(() => {
            const timeSpent = Date.now() - startTime;
            this.userProgress.user.totalTimeSpent += timeSpent;
            startTime = Date.now();
            this.saveProgress();
        }, 60000); // كل دقيقة
    }

    formatTime(milliseconds) {
        const hours = Math.floor(milliseconds / (1000 * 60 * 60));
        const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60));
        
        if (hours > 0) {
            return `${hours} ساعة ${minutes} دقيقة`;
        } else {
            return `${minutes} دقيقة`;
        }
    }

    getRecentActivity() {
        // جمع النشاطات الأخيرة
        const activities = [];
        
        // إضافة الصفحات المزارة مؤخراً
        Object.entries(this.userProgress.pages).forEach(([pageId, data]) => {
            if (data.lastVisit) {
                activities.push({
                    type: 'page_visit',
                    date: new Date(data.lastVisit),
                    description: `زيارة ${this.getPageTitle(pageId)}`
                });
            }
        });
        
        // إضافة الاختبارات المكتملة
        Object.entries(this.userProgress.quizzes).forEach(([quizId, data]) => {
            activities.push({
                type: 'quiz_completed',
                date: new Date(data.date),
                description: `إكمال اختبار ${this.getPageTitle(quizId)} - ${data.percentage}%`
            });
        });
        
        // ترتيب حسب التاريخ
        activities.sort((a, b) => b.date - a.date);
        
        // عرض آخر 5 نشاطات
        return activities.slice(0, 5).map(activity => `
            <div class="activity-item">
                <i class="fas ${activity.type === 'page_visit' ? 'fa-eye' : 'fa-check-circle'}"></i>
                <div class="activity-content">
                    <span class="activity-description">${activity.description}</span>
                    <span class="activity-date">${activity.date.toLocaleDateString('ar-SA')}</span>
                </div>
            </div>
        `).join('') || '<p class="no-activity">لا توجد نشاطات حديثة</p>';
    }

    getRecommendations() {
        const recommendations = [];
        const overallProgress = this.calculateOverallProgress();
        
        if (overallProgress < 25) {
            recommendations.push('ابدأ بالجزء الأول لفهم أساسيات معايير AAMI');
        } else if (overallProgress < 50) {
            recommendations.push('انتقل إلى الجزء الثاني لتعلم التطبيقات العملية');
        } else if (overallProgress < 75) {
            recommendations.push('أكمل الجزء الثالث للمواضيع المتقدمة');
        } else {
            recommendations.push('ممتاز! راجع المواضيع التي تحتاج تعزيز');
        }
        
        // إضافة توصيات بناءً على الأداء
        const quizScores = Object.values(this.userProgress.quizzes).map(q => q.percentage);
        const averageScore = quizScores.length > 0 ? quizScores.reduce((a, b) => a + b, 0) / quizScores.length : 0;
        
        if (averageScore < 70) {
            recommendations.push('راجع المواد وأعد الاختبارات لتحسين النتائج');
        }
        
        return recommendations.map(rec => `
            <div class="recommendation-item">
                <i class="fas fa-lightbulb"></i>
                <span>${rec}</span>
            </div>
        `).join('') || '<p class="no-recommendations">لا توجد توصيات حالياً</p>';
    }

    getPageTitle(pageId) {
        const [partId, chapterId] = pageId.split('_');
        const part = this.courseStructure[partId];
        if (part && part.chapters[chapterId]) {
            return part.chapters[chapterId].title;
        }
        return pageId;
    }

    calculateDetailedStats() {
        const quizzes = Object.values(this.userProgress.quizzes);
        const pages = Object.values(this.userProgress.pages);
        
        return {
            pagesVisited: Object.keys(this.userProgress.pages).length,
            quizzesCompleted: Object.keys(this.userProgress.quizzes).length,
            checklistsCompleted: Object.keys(this.userProgress.checklists).length,
            bookmarks: this.userProgress.bookmarks.length,
            averageQuizScore: quizzes.length > 0 ? Math.round(quizzes.reduce((sum, q) => sum + q.percentage, 0) / quizzes.length) : 0,
            bestQuizScore: quizzes.length > 0 ? Math.max(...quizzes.map(q => q.percentage)) : 0,
            totalVisits: pages.reduce((sum, p) => sum + p.visits, 0),
            averageTimePerPage: pages.length > 0 ? pages.reduce((sum, p) => sum + p.timeSpent, 0) / pages.length : 0
        };
    }

    // طرق عامة للاستخدام من الخارج
    markPageCompleted(pageId) {
        if (this.userProgress.pages[pageId]) {
            this.userProgress.pages[pageId].completed = true;
            this.saveProgress();
            this.updateProgressDisplay();
        }
    }

    addBookmark(pageId, title, section) {
        const bookmark = {
            id: Date.now().toString(),
            pageId,
            title,
            section,
            date: new Date().toISOString()
        };
        
        this.userProgress.bookmarks.push(bookmark);
        this.saveProgress();
        return bookmark.id;
    }

    removeBookmark(bookmarkId) {
        this.userProgress.bookmarks = this.userProgress.bookmarks.filter(b => b.id !== bookmarkId);
        this.saveProgress();
    }

    exportProgress() {
        const data = {
            ...this.userProgress,
            exportDate: new Date().toISOString(),
            version: '1.0'
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `aami_guide_progress_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    importProgress(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const data = JSON.parse(e.target.result);
                if (data.version && data.user) {
                    this.userProgress = data;
                    this.saveProgress();
                    this.updateProgressDisplay();
                    this.refreshDashboardContent();
                    alert('تم استيراد بيانات التقدم بنجاح!');
                } else {
                    alert('ملف غير صالح!');
                }
            } catch (error) {
                alert('خطأ في قراءة الملف!');
            }
        };
        reader.readAsText(file);
    }
}

// تهيئة نظام تتبع التقدم عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.progressTracker = new ProgressTracker();
    
    // تحقق من الإنجازات عند التحميل
    window.progressTracker.checkAchievements();
});
