/**
 * حاسبة تقييم المخاطر التفاعلية للأجهزة الطبية
 * Interactive Risk Assessment Calculator for Medical Devices
 */

class RiskCalculator {
    constructor() {
        this.riskMatrix = {
            // مصفوفة المخاطر: [الاحتمالية][الشدة] = مستوى المخاطر
            1: { 1: 1, 2: 2, 3: 3, 4: 4 },  // نادر
            2: { 1: 2, 2: 4, 3: 6, 4: 8 },  // محتمل
            3: { 1: 3, 2: 6, 3: 9, 4: 12 }, // مرجح
            4: { 1: 4, 2: 8, 3: 12, 4: 16 } // شبه مؤكد
        };
        
        this.probabilityLabels = {
            1: 'نادر',
            2: 'محتمل', 
            3: 'مرجح',
            4: 'شبه مؤكد'
        };
        
        this.severityLabels = {
            1: 'طفيفة',
            2: 'متوسطة',
            3: 'خطيرة', 
            4: 'كارثية'
        };
        
        this.riskLevels = {
            1: { level: 'منخفض', color: '#4CAF50', action: 'مقبول - مراقبة دورية' },
            2: { level: 'منخفض', color: '#4CAF50', action: 'مقبول - مراقبة دورية' },
            3: { level: 'متوسط', color: '#FF9800', action: 'يتطلب إجراءات تحكم' },
            4: { level: 'متوسط', color: '#FF9800', action: 'يتطلب إجراءات تحكم' },
            6: { level: 'عالي', color: '#f44336', action: 'يتطلب إجراءات فورية' },
            8: { level: 'عالي', color: '#f44336', action: 'يتطلب إجراءات فورية' },
            9: { level: 'عالي جداً', color: '#8B0000', action: 'غير مقبول - إيقاف فوري' },
            12: { level: 'عالي جداً', color: '#8B0000', action: 'غير مقبول - إيقاف فوري' },
            16: { level: 'عالي جداً', color: '#8B0000', action: 'غير مقبول - إيقاف فوري' }
        };
        
        this.init();
    }

    init() {
        this.createCalculatorInterface();
        this.setupEventListeners();
    }

    createCalculatorInterface() {
        // البحث عن مكان إدراج الحاسبة في صفحة إدارة المخاطر
        const riskSection = document.querySelector('#section-5-3');
        if (riskSection) {
            this.addCalculatorToSection(riskSection);
        }
    }

    addCalculatorToSection(section) {
        const calculatorDiv = document.createElement('div');
        calculatorDiv.className = 'risk-calculator-container';
        calculatorDiv.innerHTML = `
            <div class="risk-calculator">
                <div class="calculator-header">
                    <h4>
                        <i class="fas fa-calculator"></i>
                        حاسبة تقييم المخاطر التفاعلية
                    </h4>
                    <p>استخدم هذه الأداة لحساب مستوى المخاطر بناءً على الاحتمالية والشدة</p>
                </div>
                
                <div class="calculator-inputs">
                    <div class="input-group">
                        <label for="probability-select">
                            <i class="fas fa-percentage"></i>
                            احتمالية الحدوث
                        </label>
                        <select id="probability-select" class="calculator-select">
                            <option value="">اختر الاحتمالية</option>
                            <option value="1">نادر - أقل من مرة واحدة في السنة</option>
                            <option value="2">محتمل - مرة إلى عدة مرات في السنة</option>
                            <option value="3">مرجح - مرة إلى عدة مرات في الشهر</option>
                            <option value="4">شبه مؤكد - مرة أو أكثر في الأسبوع</option>
                        </select>
                    </div>
                    
                    <div class="input-group">
                        <label for="severity-select">
                            <i class="fas fa-exclamation-triangle"></i>
                            شدة العواقب
                        </label>
                        <select id="severity-select" class="calculator-select">
                            <option value="">اختر الشدة</option>
                            <option value="1">طفيفة - إزعاج بسيط، لا توجد إصابات</option>
                            <option value="2">متوسطة - إصابة طفيفة، علاج بسيط</option>
                            <option value="3">خطيرة - إصابة خطيرة، علاج مكثف</option>
                            <option value="4">كارثية - وفاة أو إعاقة دائمة</option>
                        </select>
                    </div>
                    
                    <button id="calculate-risk-btn" class="btn btn-primary calculator-btn" disabled>
                        <i class="fas fa-calculator"></i>
                        احسب مستوى المخاطر
                    </button>
                </div>
                
                <div id="risk-result" class="risk-result" style="display: none;">
                    <!-- سيتم عرض النتيجة هنا -->
                </div>
                
                <div class="risk-matrix-display">
                    <h5>مصفوفة تقييم المخاطر</h5>
                    <div class="matrix-table">
                        <table class="risk-matrix-table">
                            <thead>
                                <tr>
                                    <th>الشدة/الاحتمالية</th>
                                    <th>نادر</th>
                                    <th>محتمل</th>
                                    <th>مرجح</th>
                                    <th>شبه مؤكد</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="severity-label">كارثية</td>
                                    <td class="risk-cell" data-risk="4">4</td>
                                    <td class="risk-cell" data-risk="8">8</td>
                                    <td class="risk-cell" data-risk="12">12</td>
                                    <td class="risk-cell" data-risk="16">16</td>
                                </tr>
                                <tr>
                                    <td class="severity-label">خطيرة</td>
                                    <td class="risk-cell" data-risk="3">3</td>
                                    <td class="risk-cell" data-risk="6">6</td>
                                    <td class="risk-cell" data-risk="9">9</td>
                                    <td class="risk-cell" data-risk="12">12</td>
                                </tr>
                                <tr>
                                    <td class="severity-label">متوسطة</td>
                                    <td class="risk-cell" data-risk="2">2</td>
                                    <td class="risk-cell" data-risk="4">4</td>
                                    <td class="risk-cell" data-risk="6">6</td>
                                    <td class="risk-cell" data-risk="8">8</td>
                                </tr>
                                <tr>
                                    <td class="severity-label">طفيفة</td>
                                    <td class="risk-cell" data-risk="1">1</td>
                                    <td class="risk-cell" data-risk="2">2</td>
                                    <td class="risk-cell" data-risk="3">3</td>
                                    <td class="risk-cell" data-risk="4">4</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <div class="risk-examples">
                    <h5>أمثلة تطبيقية</h5>
                    <div class="examples-grid">
                        <div class="example-card" data-example="ventilator">
                            <h6>جهاز التنفس الصناعي</h6>
                            <p>انقطاع التيار الكهربائي</p>
                            <button class="btn btn-secondary example-btn">احسب المخاطر</button>
                        </div>
                        <div class="example-card" data-example="monitor">
                            <h6>جهاز مراقبة المريض</h6>
                            <p>خطأ في قراءة ضغط الدم</p>
                            <button class="btn btn-secondary example-btn">احسب المخاطر</button>
                        </div>
                        <div class="example-card" data-example="infusion">
                            <h6>مضخة الحقن</h6>
                            <p>جرعة زائدة من الدواء</p>
                            <button class="btn btn-secondary example-btn">احسب المخاطر</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        section.appendChild(calculatorDiv);
        this.styleRiskMatrix();
    }

    setupEventListeners() {
        document.addEventListener('change', (e) => {
            if (e.target.id === 'probability-select' || e.target.id === 'severity-select') {
                this.updateCalculateButton();
            }
        });

        document.addEventListener('click', (e) => {
            if (e.target.id === 'calculate-risk-btn') {
                this.calculateRisk();
            }
            
            if (e.target.classList.contains('example-btn')) {
                const exampleType = e.target.closest('.example-card').dataset.example;
                this.loadExample(exampleType);
            }
        });
    }

    updateCalculateButton() {
        const probabilitySelect = document.getElementById('probability-select');
        const severitySelect = document.getElementById('severity-select');
        const calculateBtn = document.getElementById('calculate-risk-btn');
        
        if (probabilitySelect && severitySelect && calculateBtn) {
            const hasValues = probabilitySelect.value && severitySelect.value;
            calculateBtn.disabled = !hasValues;
        }
    }

    calculateRisk() {
        const probabilitySelect = document.getElementById('probability-select');
        const severitySelect = document.getElementById('severity-select');
        const resultDiv = document.getElementById('risk-result');
        
        if (!probabilitySelect || !severitySelect || !resultDiv) return;
        
        const probability = parseInt(probabilitySelect.value);
        const severity = parseInt(severitySelect.value);
        
        if (!probability || !severity) return;
        
        const riskScore = this.riskMatrix[probability][severity];
        const riskInfo = this.riskLevels[riskScore];
        
        this.displayResult(probability, severity, riskScore, riskInfo, resultDiv);
        this.highlightMatrixCell(probability, severity, riskScore);
    }

    displayResult(probability, severity, riskScore, riskInfo, resultDiv) {
        resultDiv.innerHTML = `
            <div class="risk-result-content" style="border-color: ${riskInfo.color}">
                <div class="risk-score" style="background: ${riskInfo.color}">
                    <span class="score-number">${riskScore}</span>
                    <span class="score-level">${riskInfo.level}</span>
                </div>
                
                <div class="risk-details">
                    <div class="risk-calculation">
                        <h6>التفاصيل:</h6>
                        <p>
                            <strong>الاحتمالية:</strong> ${this.probabilityLabels[probability]} (${probability})
                            <br>
                            <strong>الشدة:</strong> ${this.severityLabels[severity]} (${severity})
                            <br>
                            <strong>مستوى المخاطر:</strong> ${probability} × ${severity} = ${riskScore}
                        </p>
                    </div>
                    
                    <div class="risk-action">
                        <h6>الإجراء المطلوب:</h6>
                        <p>${riskInfo.action}</p>
                    </div>
                    
                    <div class="risk-recommendations">
                        <h6>توصيات للتحكم في المخاطر:</h6>
                        ${this.getRecommendations(riskScore)}
                    </div>
                </div>
            </div>
        `;
        
        resultDiv.style.display = 'block';
        resultDiv.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }

    getRecommendations(riskScore) {
        const recommendations = {
            1: '<ul><li>مراقبة دورية</li><li>توثيق الحوادث</li><li>مراجعة سنوية</li></ul>',
            2: '<ul><li>مراقبة دورية</li><li>توثيق الحوادث</li><li>مراجعة سنوية</li></ul>',
            3: '<ul><li>تطبيق إجراءات تحكم إضافية</li><li>تدريب المستخدمين</li><li>مراقبة شهرية</li></ul>',
            4: '<ul><li>تطبيق إجراءات تحكم إضافية</li><li>تدريب المستخدمين</li><li>مراقبة شهرية</li></ul>',
            6: '<ul><li>إجراءات تحكم فورية</li><li>تدريب مكثف</li><li>مراقبة أسبوعية</li><li>مراجعة الإجراءات</li></ul>',
            8: '<ul><li>إجراءات تحكم فورية</li><li>تدريب مكثف</li><li>مراقبة أسبوعية</li><li>مراجعة الإجراءات</li></ul>',
            9: '<ul><li>إيقاف الاستخدام فوراً</li><li>تحقيق شامل</li><li>إعادة تصميم النظام</li><li>موافقة إدارية للاستئناف</li></ul>',
            12: '<ul><li>إيقاف الاستخدام فوراً</li><li>تحقيق شامل</li><li>إعادة تصميم النظام</li><li>موافقة إدارية للاستئناف</li></ul>',
            16: '<ul><li>إيقاف الاستخدام فوراً</li><li>تحقيق شامل</li><li>إعادة تصميم النظام</li><li>موافقة إدارية للاستئناف</li></ul>'
        };
        
        return recommendations[riskScore] || '<p>يرجى مراجعة خبير إدارة المخاطر</p>';
    }

    highlightMatrixCell(probability, severity, riskScore) {
        // إزالة التمييز السابق
        document.querySelectorAll('.risk-cell').forEach(cell => {
            cell.classList.remove('highlighted');
        });
        
        // تمييز الخلية المحددة
        const targetCell = document.querySelector(`[data-risk="${riskScore}"]`);
        if (targetCell) {
            targetCell.classList.add('highlighted');
        }
    }

    styleRiskMatrix() {
        const cells = document.querySelectorAll('.risk-cell');
        cells.forEach(cell => {
            const riskValue = parseInt(cell.dataset.risk);
            const riskInfo = this.riskLevels[riskValue];
            if (riskInfo) {
                cell.style.backgroundColor = riskInfo.color;
                cell.style.color = 'white';
                cell.style.fontWeight = 'bold';
            }
        });
    }

    loadExample(exampleType) {
        const examples = {
            ventilator: { probability: 2, severity: 4 }, // محتمل + كارثية
            monitor: { probability: 3, severity: 2 },    // مرجح + متوسطة  
            infusion: { probability: 2, severity: 3 }    // محتمل + خطيرة
        };
        
        const example = examples[exampleType];
        if (example) {
            document.getElementById('probability-select').value = example.probability;
            document.getElementById('severity-select').value = example.severity;
            this.updateCalculateButton();
            this.calculateRisk();
        }
    }
}

// تهيئة حاسبة المخاطر عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    // التحقق من وجود صفحة إدارة المخاطر
    if (document.querySelector('#section-5-3')) {
        new RiskCalculator();
    }
});
