/**
 * محرك البحث المتقدم
 * Advanced Search Engine
 */

class AdvancedSearchEngine {
    constructor() {
        this.searchIndex = {};
        this.contentDatabase = {};
        this.searchHistory = [];
        this.maxHistoryItems = 10;
        
        this.init();
    }

    init() {
        this.buildSearchIndex();
        this.enhanceExistingSearch();
        this.setupAdvancedFeatures();
    }

    buildSearchIndex() {
        // فهرسة محتوى الصفحات
        this.contentDatabase = {
            // الجزء الأول
            'part1_chapter1': {
                title: 'مقدمة في معايير AAMI',
                content: 'معايير AAMI الأجهزة الطبية السلامة الفعالية التنظيم الصحي المستشفيات العناية المركزة',
                type: 'chapter',
                part: 'part1',
                url: 'pages/part1/chapter1.html',
                keywords: ['AAMI', 'معايير', 'أجهزة طبية', 'سلامة', 'فعالية'],
                lastModified: '2024-01-15'
            },
            'part1_chapter2': {
                title: 'دور مهندس الطب الحيوي',
                content: 'مهندس سريري طب حيوي مسؤوليات صيانة تشغيل إدارة أجهزة طبية مستشفى',
                type: 'chapter',
                part: 'part1',
                url: 'pages/part1/chapter2.html',
                keywords: ['مهندس سريري', 'طب حيوي', 'مسؤوليات', 'صيانة'],
                lastModified: '2024-01-16'
            },
            'part1_chapter3': {
                title: 'نظرة شاملة على معايير AAMI',
                content: 'معايير AAMI شاملة تصنيف أنواع تطبيقات عملية إرشادات توجيهات',
                type: 'chapter',
                part: 'part1',
                url: 'pages/part1/chapter3.html',
                keywords: ['معايير AAMI', 'تصنيف', 'تطبيقات', 'إرشادات'],
                lastModified: '2024-01-17'
            },
            
            // الجزء الثاني
            'part2_chapter4': {
                title: 'دورة حياة الأجهزة الطبية',
                content: 'دورة حياة أجهزة طبية تخطيط شراء تركيب تشغيل صيانة استبدال',
                type: 'chapter',
                part: 'part2',
                url: 'pages/part2/chapter4.html',
                keywords: ['دورة حياة', 'أجهزة طبية', 'تخطيط', 'شراء', 'تركيب'],
                lastModified: '2024-01-18'
            },
            'part2_chapter5': {
                title: 'إدارة المخاطر',
                content: 'إدارة مخاطر تقييم تحليل مصفوفة احتمالية شدة تحكم ISO 14971',
                type: 'chapter',
                part: 'part2',
                url: 'pages/part2/chapter5.html',
                keywords: ['إدارة المخاطر', 'تقييم', 'تحليل', 'ISO 14971'],
                lastModified: '2024-01-19'
            },
            'part2_chapter6': {
                title: 'الصيانة الوقائية',
                content: 'صيانة وقائية جدولة فحص اختبار معايرة تنظيف تطهير',
                type: 'chapter',
                part: 'part2',
                url: 'pages/part2/chapter6.html',
                keywords: ['صيانة وقائية', 'جدولة', 'فحص', 'معايرة'],
                lastModified: '2024-01-20'
            },
            
            // الجزء الثالث
            'part3_chapter7': {
                title: 'الأمن السيبراني',
                content: 'أمن سيبراني حماية شبكات تشفير مصادقة ثغرات هجمات',
                type: 'chapter',
                part: 'part3',
                url: 'pages/part3/chapter7.html',
                keywords: ['أمن سيبراني', 'حماية', 'شبكات', 'تشفير'],
                lastModified: '2024-01-21'
            },
            'part3_chapter8': {
                title: 'التشغيل البيني',
                content: 'تشغيل بيني اتصال شبكات بروتوكولات HL7 DICOM تكامل',
                type: 'chapter',
                part: 'part3',
                url: 'pages/part3/chapter8.html',
                keywords: ['تشغيل بيني', 'اتصال', 'HL7', 'DICOM'],
                lastModified: '2024-01-22'
            },
            'part3_chapter9': {
                title: 'التحضير للتدقيق',
                content: 'تدقيق اعتماد JCI CBAHI توثيق إجراءات امتثال',
                type: 'chapter',
                part: 'part3',
                url: 'pages/part3/chapter9.html',
                keywords: ['تدقيق', 'اعتماد', 'JCI', 'CBAHI'],
                lastModified: '2024-01-23'
            },
            
            // الاختبارات
            'quiz_part1': {
                title: 'اختبار الجزء الأول',
                content: 'اختبار تقييم أسئلة إجابات نتائج درجات',
                type: 'quiz',
                part: 'part1',
                url: '#quiz-part1',
                keywords: ['اختبار', 'تقييم', 'أسئلة'],
                lastModified: '2024-01-24'
            },
            
            // قوائم الفحص
            'checklist_maintenance': {
                title: 'قائمة فحص الصيانة الوقائية',
                content: 'قائمة فحص صيانة وقائية عناصر خطوات إجراءات',
                type: 'checklist',
                part: 'part2',
                url: '#checklist-maintenance',
                keywords: ['قائمة فحص', 'صيانة وقائية'],
                lastModified: '2024-01-25'
            },
            
            // مسرد المصطلحات
            'glossary_aami': {
                title: 'AAMI - جمعية تطوير الأجهزة الطبية',
                content: 'AAMI Association Advancement Medical Instrumentation منظمة معايير',
                type: 'glossary',
                part: 'general',
                url: '#glossary-aami',
                keywords: ['AAMI', 'منظمة', 'معايير'],
                lastModified: '2024-01-26'
            }
        };

        // بناء فهرس البحث
        this.buildInvertedIndex();
    }

    buildInvertedIndex() {
        this.searchIndex = {};
        
        Object.entries(this.contentDatabase).forEach(([id, item]) => {
            // فهرسة العنوان
            this.indexText(item.title, id, 3); // وزن أعلى للعنوان
            
            // فهرسة المحتوى
            this.indexText(item.content, id, 1);
            
            // فهرسة الكلمات المفتاحية
            item.keywords.forEach(keyword => {
                this.indexText(keyword, id, 2); // وزن متوسط للكلمات المفتاحية
            });
        });
    }

    indexText(text, documentId, weight = 1) {
        const words = this.tokenize(text);
        
        words.forEach(word => {
            if (!this.searchIndex[word]) {
                this.searchIndex[word] = {};
            }
            
            if (!this.searchIndex[word][documentId]) {
                this.searchIndex[word][documentId] = 0;
            }
            
            this.searchIndex[word][documentId] += weight;
        });
    }

    tokenize(text) {
        // تنظيف النص وتقسيمه إلى كلمات
        return text
            .toLowerCase()
            .replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFFa-zA-Z0-9\s]/g, ' ')
            .split(/\s+/)
            .filter(word => word.length >= 2);
    }

    enhanceExistingSearch() {
        // تحسين نظام البحث الموجود
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            // إزالة المستمع القديم وإضافة الجديد
            searchInput.removeEventListener('input', this.handleSearch);
            searchInput.addEventListener('input', this.debounce((e) => {
                this.performAdvancedSearch(e.target.value);
            }, 300));
        }
    }

    setupAdvancedFeatures() {
        // إضافة ميزات متقدمة للبحث الموجود
        this.addSearchSuggestions();
        this.addSearchHistory();
        this.addSearchFilters();
    }

    addSearchSuggestions() {
        const searchContainer = document.querySelector('.search-container');
        if (!searchContainer) return;

        const suggestionsDiv = document.createElement('div');
        suggestionsDiv.className = 'search-suggestions';
        suggestionsDiv.innerHTML = `
            <div class="suggestions-header">
                <h4>اقتراحات البحث:</h4>
            </div>
            <div class="suggestion-tags">
                <span class="suggestion-tag" data-query="معايير AAMI">معايير AAMI</span>
                <span class="suggestion-tag" data-query="إدارة المخاطر">إدارة المخاطر</span>
                <span class="suggestion-tag" data-query="الصيانة الوقائية">الصيانة الوقائية</span>
                <span class="suggestion-tag" data-query="الأمن السيبراني">الأمن السيبراني</span>
                <span class="suggestion-tag" data-query="التشغيل البيني">التشغيل البيني</span>
                <span class="suggestion-tag" data-query="مهندس سريري">مهندس سريري</span>
            </div>
        `;

        const searchResults = document.getElementById('search-results');
        if (searchResults) {
            searchResults.appendChild(suggestionsDiv);
        }

        // إضافة مستمعي الأحداث للاقتراحات
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('suggestion-tag')) {
                const query = e.target.dataset.query;
                const searchInput = document.getElementById('search-input');
                if (searchInput) {
                    searchInput.value = query;
                    this.performAdvancedSearch(query);
                }
            }
        });
    }

    addSearchHistory() {
        // تحميل تاريخ البحث من التخزين المحلي
        try {
            this.searchHistory = JSON.parse(localStorage.getItem('aami_search_history') || '[]');
        } catch {
            this.searchHistory = [];
        }
    }

    addSearchFilters() {
        const searchContainer = document.querySelector('.search-container');
        if (!searchContainer) return;

        const filtersDiv = document.createElement('div');
        filtersDiv.className = 'search-filters';
        filtersDiv.innerHTML = `
            <div class="filter-group">
                <label>نوع المحتوى:</label>
                <select id="content-type-filter">
                    <option value="">جميع الأنواع</option>
                    <option value="chapter">فصول</option>
                    <option value="quiz">اختبارات</option>
                    <option value="checklist">قوائم فحص</option>
                    <option value="glossary">مسرد المصطلحات</option>
                </select>
            </div>
            
            <div class="filter-group">
                <label>الجزء:</label>
                <select id="part-filter">
                    <option value="">جميع الأجزاء</option>
                    <option value="part1">الجزء الأول</option>
                    <option value="part2">الجزء الثاني</option>
                    <option value="part3">الجزء الثالث</option>
                </select>
            </div>
        `;

        const searchHeader = document.querySelector('.search-header');
        if (searchHeader) {
            searchHeader.insertAdjacentElement('afterend', filtersDiv);
        }

        // إضافة مستمعي الأحداث للفلاتر
        ['content-type-filter', 'part-filter'].forEach(filterId => {
            const filter = document.getElementById(filterId);
            if (filter) {
                filter.addEventListener('change', () => {
                    const searchInput = document.getElementById('search-input');
                    if (searchInput && searchInput.value.trim()) {
                        this.performAdvancedSearch(searchInput.value.trim());
                    }
                });
            }
        });
    }

    performAdvancedSearch(query) {
        if (!query || query.length < 2) {
            this.clearSearchResults();
            return;
        }

        const startTime = performance.now();
        
        // إضافة إلى تاريخ البحث
        this.addToSearchHistory(query);
        
        // البحث في الفهرس
        const results = this.searchInIndex(query);
        
        // تطبيق الفلاتر
        const filteredResults = this.applyFilters(results);
        
        // ترتيب النتائج
        const sortedResults = this.sortResults(filteredResults, query);
        
        const endTime = performance.now();
        const searchTime = ((endTime - startTime) / 1000).toFixed(3);
        
        // عرض النتائج
        this.displayAdvancedResults(sortedResults, query, searchTime);
    }

    searchInIndex(query) {
        const queryWords = this.tokenize(query);
        const documentScores = {};
        
        queryWords.forEach(word => {
            if (this.searchIndex[word]) {
                Object.entries(this.searchIndex[word]).forEach(([docId, score]) => {
                    if (!documentScores[docId]) {
                        documentScores[docId] = 0;
                    }
                    documentScores[docId] += score;
                });
            }
        });
        
        // تحويل النتائج إلى مصفوفة مع المعلومات
        return Object.entries(documentScores).map(([docId, score]) => ({
            id: docId,
            score: score,
            document: this.contentDatabase[docId]
        }));
    }

    applyFilters(results) {
        const contentTypeFilter = document.getElementById('content-type-filter')?.value;
        const partFilter = document.getElementById('part-filter')?.value;
        
        return results.filter(result => {
            if (contentTypeFilter && result.document.type !== contentTypeFilter) {
                return false;
            }
            
            if (partFilter && result.document.part !== partFilter) {
                return false;
            }
            
            return true;
        });
    }

    sortResults(results, query) {
        // ترتيب حسب النقاط أولاً، ثم حسب الصلة بالاستعلام
        return results.sort((a, b) => {
            // ترتيب حسب النقاط
            if (b.score !== a.score) {
                return b.score - a.score;
            }
            
            // إذا كانت النقاط متساوية، ترتيب حسب تطابق العنوان
            const aTitle = a.document.title.toLowerCase();
            const bTitle = b.document.title.toLowerCase();
            const queryLower = query.toLowerCase();
            
            const aExactMatch = aTitle.includes(queryLower);
            const bExactMatch = bTitle.includes(queryLower);
            
            if (aExactMatch && !bExactMatch) return -1;
            if (!aExactMatch && bExactMatch) return 1;
            
            return 0;
        });
    }

    displayAdvancedResults(results, query, searchTime) {
        const searchResults = document.getElementById('search-results');
        if (!searchResults) return;

        if (results.length === 0) {
            searchResults.innerHTML = `
                <div class="no-results">
                    <i class="fas fa-search"></i>
                    <h4>لم يتم العثور على نتائج</h4>
                    <p>جرب استخدام كلمات مختلفة أو تحقق من الإملاء</p>
                    ${this.getSearchSuggestions(query)}
                </div>
            `;
            return;
        }

        const resultsHTML = `
            <div class="search-stats">
                <span class="results-count">${results.length} نتيجة</span>
                <span class="search-time">في ${searchTime} ثانية</span>
            </div>
            
            <div class="search-results-list">
                ${results.map(result => this.createResultHTML(result, query)).join('')}
            </div>
        `;

        searchResults.innerHTML = resultsHTML;
    }

    createResultHTML(result, query) {
        const doc = result.document;
        const highlightedTitle = this.highlightText(doc.title, query);
        const highlightedContent = this.highlightText(doc.content.substring(0, 150) + '...', query);
        
        const typeIcon = this.getTypeIcon(doc.type);
        const typeLabel = this.getTypeLabel(doc.type);
        
        return `
            <div class="search-result-item" data-url="${doc.url}">
                <div class="result-header">
                    <div class="result-type">
                        <i class="${typeIcon}"></i>
                        <span>${typeLabel}</span>
                    </div>
                    <div class="result-score">${Math.round(result.score * 10)}%</div>
                </div>
                
                <h4 class="result-title">
                    <a href="${doc.url}">${highlightedTitle}</a>
                </h4>
                
                <p class="result-content">${highlightedContent}</p>
                
                <div class="result-meta">
                    <span class="result-part">${this.getPartLabel(doc.part)}</span>
                    <span class="result-date">آخر تحديث: ${new Date(doc.lastModified).toLocaleDateString('ar-SA')}</span>
                </div>
            </div>
        `;
    }

    highlightText(text, query) {
        const queryWords = this.tokenize(query);
        let highlightedText = text;
        
        queryWords.forEach(word => {
            const regex = new RegExp(`(${word})`, 'gi');
            highlightedText = highlightedText.replace(regex, '<mark>$1</mark>');
        });
        
        return highlightedText;
    }

    getTypeIcon(type) {
        const icons = {
            chapter: 'fas fa-book',
            quiz: 'fas fa-question-circle',
            checklist: 'fas fa-list-check',
            glossary: 'fas fa-book-open'
        };
        return icons[type] || 'fas fa-file';
    }

    getTypeLabel(type) {
        const labels = {
            chapter: 'فصل',
            quiz: 'اختبار',
            checklist: 'قائمة فحص',
            glossary: 'مصطلح'
        };
        return labels[type] || 'محتوى';
    }

    getPartLabel(part) {
        const labels = {
            part1: 'الجزء الأول',
            part2: 'الجزء الثاني',
            part3: 'الجزء الثالث',
            general: 'عام'
        };
        return labels[part] || part;
    }

    getSearchSuggestions(query) {
        // اقتراح كلمات بديلة بناءً على الاستعلام
        const suggestions = [];
        const queryWords = this.tokenize(query);
        
        // البحث عن كلمات مشابهة في الفهرس
        Object.keys(this.searchIndex).forEach(word => {
            queryWords.forEach(queryWord => {
                if (word.includes(queryWord) || queryWord.includes(word)) {
                    if (!suggestions.includes(word) && word !== queryWord) {
                        suggestions.push(word);
                    }
                }
            });
        });
        
        if (suggestions.length > 0) {
            return `
                <div class="search-suggestions">
                    <h5>ربما تقصد:</h5>
                    <div class="suggestion-tags">
                        ${suggestions.slice(0, 5).map(suggestion => 
                            `<span class="suggestion-tag" data-query="${suggestion}">${suggestion}</span>`
                        ).join('')}
                    </div>
                </div>
            `;
        }
        
        return '';
    }

    addToSearchHistory(query) {
        // إزالة الاستعلام إذا كان موجوداً مسبقاً
        this.searchHistory = this.searchHistory.filter(item => item !== query);
        
        // إضافة الاستعلام في المقدمة
        this.searchHistory.unshift(query);
        
        // الاحتفاظ بعدد محدود من العناصر
        this.searchHistory = this.searchHistory.slice(0, this.maxHistoryItems);
        
        // حفظ في التخزين المحلي
        try {
            localStorage.setItem('aami_search_history', JSON.stringify(this.searchHistory));
        } catch (error) {
            console.warn('خطأ في حفظ تاريخ البحث:', error);
        }
    }

    clearSearchResults() {
        const searchResults = document.getElementById('search-results');
        if (searchResults) {
            searchResults.innerHTML = `
                <div class="search-placeholder">
                    <i class="fas fa-search"></i>
                    <p>ابدأ بكتابة كلمة للبحث في محتويات الدليل</p>
                </div>
            `;
            
            // إعادة إضافة الاقتراحات
            this.addSearchSuggestions();
        }
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // طرق عامة للاستخدام من الخارج
    addToIndex(id, title, content, type, part, url, keywords = []) {
        this.contentDatabase[id] = {
            title,
            content,
            type,
            part,
            url,
            keywords,
            lastModified: new Date().toISOString()
        };
        
        // إعادة بناء الفهرس
        this.buildInvertedIndex();
    }

    removeFromIndex(id) {
        delete this.contentDatabase[id];
        this.buildInvertedIndex();
    }

    getSearchHistory() {
        return this.searchHistory;
    }

    clearSearchHistory() {
        this.searchHistory = [];
        localStorage.removeItem('aami_search_history');
    }
}

// تهيئة محرك البحث المتقدم عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.advancedSearchEngine = new AdvancedSearchEngine();
});
