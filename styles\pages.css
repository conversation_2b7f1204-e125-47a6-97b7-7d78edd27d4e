/* Page-specific styles for AAMI Standards Guide */

/* Page Header */
.page-header {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    padding: 120px 0 var(--spacing-3xl);
    text-align: center;
}

.breadcrumb {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-size-sm);
    opacity: 0.9;
}

.breadcrumb a {
    color: white;
    text-decoration: none;
    transition: var(--transition-fast);
}

.breadcrumb a:hover {
    opacity: 0.8;
}

.breadcrumb i {
    font-size: var(--font-size-xs);
}

.page-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    color: white;
}

.page-subtitle {
    font-size: var(--font-size-lg);
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Table of Contents Styles */
.toc-section {
    padding: var(--spacing-3xl) 0;
    background: white;
}

.toc-part {
    margin-bottom: var(--spacing-3xl);
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-medium);
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.toc-part .part-header {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    padding: var(--spacing-xl);
    text-align: center;
}

.toc-part .part-number {
    display: block;
    font-size: var(--font-size-sm);
    opacity: 0.9;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
}

.toc-part .part-header h2 {
    margin: 0;
    color: white;
    font-size: var(--font-size-2xl);
}

.toc-chapter {
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--border-color);
}

.toc-chapter:last-child {
    border-bottom: none;
}

.chapter-header {
    margin-bottom: var(--spacing-lg);
}

.chapter-header h3 {
    margin: 0;
    font-size: var(--font-size-xl);
}

.chapter-header h3 a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

.chapter-header h3 a:hover {
    color: var(--accent-color);
}

.section-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.section-list li {
    margin-bottom: var(--spacing-sm);
}

.section-list a {
    display: block;
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
    border-right: 3px solid transparent;
}

.section-list a:hover {
    background: var(--light-color);
    color: var(--primary-color);
    border-right-color: var(--primary-color);
}

.subsection-list {
    list-style: none;
    margin: var(--spacing-sm) 0 0 var(--spacing-xl);
    padding: 0;
}

.subsection-list li {
    margin-bottom: var(--spacing-xs);
}

.subsection-list a {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
}

.subsection-list a:hover {
    background: var(--light-color);
    color: var(--accent-color);
}

.toc-additional {
    margin-top: var(--spacing-2xl);
}

.additional-section {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-light);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
    border: 1px solid var(--border-color);
}

.additional-section h3 {
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
}

.additional-section h3 a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

.additional-section h3 a:hover {
    color: var(--accent-color);
}

/* Chapter Page Styles */
.chapter-content {
    padding: var(--spacing-3xl) 0;
    background: white;
}

.chapter-nav {
    background: var(--light-color);
    padding: var(--spacing-lg) 0;
    border-bottom: 1px solid var(--border-color);
}

.chapter-nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.nav-button {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: white;
    color: var(--primary-color);
    text-decoration: none;
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    transition: var(--transition-fast);
    font-weight: 500;
}

.nav-button:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.nav-button.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

.chapter-outline {
    background: var(--light-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
}

.chapter-outline h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.chapter-outline ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.chapter-outline li {
    margin-bottom: var(--spacing-sm);
}

.chapter-outline a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-fast);
}

.chapter-outline a:hover {
    color: var(--primary-color);
}

.content-section {
    margin-bottom: var(--spacing-2xl);
    padding-bottom: var(--spacing-xl);
    border-bottom: 1px solid var(--border-color);
}

.content-section:last-child {
    border-bottom: none;
}

.section-header {
    margin-bottom: var(--spacing-lg);
}

.section-number {
    color: var(--primary-color);
    font-weight: 600;
    margin-left: var(--spacing-sm);
}

.content-text {
    line-height: 1.8;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
}

.content-text p {
    margin-bottom: var(--spacing-md);
}

.highlight-box {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-right: 4px solid var(--primary-color);
    padding: var(--spacing-lg);
    border-radius: var(--radius-md);
    margin: var(--spacing-lg) 0;
}

.highlight-box h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.info-box {
    background: rgba(74, 144, 226, 0.1);
    border: 1px solid rgba(74, 144, 226, 0.3);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    margin: var(--spacing-lg) 0;
}

.warning-box {
    background: rgba(243, 156, 18, 0.1);
    border: 1px solid rgba(243, 156, 18, 0.3);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    margin: var(--spacing-lg) 0;
}

.success-box {
    background: rgba(39, 174, 96, 0.1);
    border: 1px solid rgba(39, 174, 96, 0.3);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    margin: var(--spacing-lg) 0;
}

/* References Page Styles */
.references-section {
    padding: var(--spacing-3xl) 0;
    background: white;
}

.reference-category {
    margin-bottom: var(--spacing-2xl);
}

.reference-category h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-color);
}

.reference-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.reference-item {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    transition: var(--transition-fast);
}

.reference-item:hover {
    box-shadow: var(--shadow-light);
    border-color: var(--primary-color);
}

.reference-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.reference-details {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.6;
}

/* Responsive Design for Pages */
@media (max-width: 768px) {
    .page-header {
        padding: 100px 0 var(--spacing-2xl);
    }
    
    .page-title {
        font-size: var(--font-size-3xl);
    }
    
    .breadcrumb {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
    
    .chapter-nav-content {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .toc-chapter {
        padding: var(--spacing-lg);
    }
    
    .subsection-list {
        margin-right: var(--spacing-lg);
    }
}

@media (max-width: 480px) {
    .page-title {
        font-size: var(--font-size-2xl);
    }
    
    .toc-part .part-header {
        padding: var(--spacing-lg);
    }
    
    .toc-chapter {
        padding: var(--spacing-md);
    }
    
    .chapter-outline {
        padding: var(--spacing-lg);
    }
}
