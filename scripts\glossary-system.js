/**
 * نظام مسرد المصطلحات التفاعلي
 * Interactive Glossary System
 */

class GlossarySystem {
    constructor() {
        this.glossaryData = {
            'AAMI': {
                term: 'AAMI',
                fullName: 'Association for the Advancement of Medical Instrumentation',
                arabic: 'جمعية تطوير الأجهزة الطبية',
                definition: 'منظمة غير ربحية تطور معايير السلامة والفعالية للأجهزة الطبية والتكنولوجيا الصحية.',
                category: 'منظمات'
            },
            'IEC 60601': {
                term: 'IEC 60601',
                fullName: 'International Electrotechnical Commission 60601',
                arabic: 'معيار اللجنة الكهروتقنية الدولية 60601',
                definition: 'سلسلة من المعايير الدولية للسلامة والأداء الأساسي للأجهزة الطبية الكهربائية.',
                category: 'معايير'
            },
            'ISO 14971': {
                term: 'ISO 14971',
                fullName: 'ISO 14971 - Medical devices - Application of risk management',
                arabic: 'معيار إدارة المخاطر للأجهزة الطبية',
                definition: 'معيار دولي يحدد عملية إدارة المخاطر للأجهزة الطبية.',
                category: 'معايير'
            },
            'FDA': {
                term: 'FDA',
                fullName: 'Food and Drug Administration',
                arabic: 'إدارة الغذاء والدواء الأمريكية',
                definition: 'وكالة فيدرالية أمريكية مسؤولة عن تنظيم وإشراف سلامة الأغذية والأدوية والأجهزة الطبية.',
                category: 'منظمات'
            },
            'Clinical Engineer': {
                term: 'Clinical Engineer',
                fullName: 'Clinical Engineer',
                arabic: 'مهندس سريري',
                definition: 'مهندس متخصص في تطبيق وإدارة التكنولوجيا الطبية في البيئة السريرية.',
                category: 'مهن'
            },
            'Biomedical Equipment': {
                term: 'Biomedical Equipment',
                fullName: 'Biomedical Equipment',
                arabic: 'أجهزة طبية حيوية',
                definition: 'الأجهزة والمعدات المستخدمة في التشخيص والعلاج والمراقبة الطبية.',
                category: 'أجهزة'
            },
            'Preventive Maintenance': {
                term: 'Preventive Maintenance',
                fullName: 'Preventive Maintenance',
                arabic: 'صيانة وقائية',
                definition: 'صيانة منتظمة ومجدولة للأجهزة لمنع الأعطال وضمان الأداء الأمثل.',
                category: 'صيانة'
            },
            'Risk Assessment': {
                term: 'Risk Assessment',
                fullName: 'Risk Assessment',
                arabic: 'تقييم المخاطر',
                definition: 'عملية منهجية لتحديد وتحليل وتقييم المخاطر المحتملة.',
                category: 'إدارة المخاطر'
            },
            'Electrical Safety': {
                term: 'Electrical Safety',
                fullName: 'Electrical Safety',
                arabic: 'السلامة الكهربائية',
                definition: 'مجموعة من الإجراءات والاختبارات لضمان الحماية من المخاطر الكهربائية.',
                category: 'سلامة'
            },
            'Calibration': {
                term: 'Calibration',
                fullName: 'Calibration',
                arabic: 'معايرة',
                definition: 'عملية ضبط دقة الأجهزة مقارنة بمعايير مرجعية معتمدة.',
                category: 'صيانة'
            },
            'Interoperability': {
                term: 'Interoperability',
                fullName: 'Interoperability',
                arabic: 'التشغيل البيني',
                definition: 'قدرة الأنظمة والأجهزة المختلفة على التواصل وتبادل البيانات بفعالية.',
                category: 'تقنية'
            },
            'HL7': {
                term: 'HL7',
                fullName: 'Health Level Seven',
                arabic: 'معيار المستوى الصحي السابع',
                definition: 'معيار لتبادل المعلومات السريرية والإدارية بين أنظمة الرعاية الصحية.',
                category: 'معايير'
            },
            'DICOM': {
                term: 'DICOM',
                fullName: 'Digital Imaging and Communications in Medicine',
                arabic: 'التصوير الرقمي والاتصالات في الطب',
                definition: 'معيار لتخزين ونقل الصور الطبية الرقمية.',
                category: 'معايير'
            },
            'Cybersecurity': {
                term: 'Cybersecurity',
                fullName: 'Cybersecurity',
                arabic: 'الأمن السيبراني',
                definition: 'حماية الأنظمة والشبكات والبيانات من الهجمات الرقمية.',
                category: 'أمان'
            },
            'JCI': {
                term: 'JCI',
                fullName: 'Joint Commission International',
                arabic: 'اللجنة المشتركة الدولية',
                definition: 'منظمة دولية لاعتماد المؤسسات الصحية وضمان جودة الرعاية.',
                category: 'اعتماد'
            },
            'CBAHI': {
                term: 'CBAHI',
                fullName: 'Central Board for Accreditation of Healthcare Institutions',
                arabic: 'المجلس المركزي لاعتماد المؤسسات الصحية',
                definition: 'هيئة سعودية لاعتماد المؤسسات الصحية وضمان جودة الخدمات الطبية.',
                category: 'اعتماد'
            }
        };
        
        this.init();
    }

    init() {
        this.createGlossaryInterface();
        this.highlightTermsInContent();
        this.setupEventListeners();
    }

    createGlossaryInterface() {
        // إنشاء زر فتح المسرد
        const glossaryButton = document.createElement('button');
        glossaryButton.className = 'glossary-toggle-btn';
        glossaryButton.innerHTML = `
            <i class="fas fa-book"></i>
            <span>مسرد المصطلحات</span>
        `;
        glossaryButton.title = 'فتح مسرد المصطلحات';
        
        // إضافة الزر إلى الصفحة
        document.body.appendChild(glossaryButton);
        
        // إنشاء نافذة المسرد
        this.createGlossaryModal();
    }

    createGlossaryModal() {
        const modal = document.createElement('div');
        modal.className = 'glossary-modal';
        modal.innerHTML = `
            <div class="glossary-modal-content">
                <div class="glossary-header">
                    <h3>
                        <i class="fas fa-book"></i>
                        مسرد المصطلحات التقنية
                    </h3>
                    <button class="glossary-close-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="glossary-search">
                    <div class="search-input-container">
                        <i class="fas fa-search"></i>
                        <input type="text" id="glossary-search" placeholder="ابحث عن مصطلح...">
                    </div>
                    <div class="glossary-filters">
                        <select id="category-filter">
                            <option value="">جميع الفئات</option>
                            ${this.getCategories().map(cat => `<option value="${cat}">${cat}</option>`).join('')}
                        </select>
                    </div>
                </div>
                
                <div class="glossary-content">
                    <div class="glossary-stats">
                        <span class="total-terms">${Object.keys(this.glossaryData).length} مصطلح</span>
                        <span class="filtered-terms" style="display: none;"></span>
                    </div>
                    
                    <div class="glossary-list">
                        ${this.renderGlossaryItems()}
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
    }

    getCategories() {
        const categories = [...new Set(Object.values(this.glossaryData).map(item => item.category))];
        return categories.sort();
    }

    renderGlossaryItems(filteredData = null) {
        const data = filteredData || this.glossaryData;
        const categories = this.getCategories();
        
        let html = '';
        
        categories.forEach(category => {
            const categoryItems = Object.values(data).filter(item => item.category === category);
            if (categoryItems.length === 0) return;
            
            html += `
                <div class="glossary-category">
                    <h4 class="category-title">
                        <i class="fas fa-folder"></i>
                        ${category}
                        <span class="category-count">(${categoryItems.length})</span>
                    </h4>
                    <div class="category-items">
                        ${categoryItems.map(item => this.renderGlossaryItem(item)).join('')}
                    </div>
                </div>
            `;
        });
        
        return html || '<div class="no-results"><i class="fas fa-search"></i><p>لم يتم العثور على نتائج</p></div>';
    }

    renderGlossaryItem(item) {
        return `
            <div class="glossary-item" data-term="${item.term}">
                <div class="term-header">
                    <h5 class="term-name">${item.term}</h5>
                    <span class="term-arabic">${item.arabic}</span>
                </div>
                <div class="term-full-name">${item.fullName}</div>
                <div class="term-definition">${item.definition}</div>
                <div class="term-actions">
                    <button class="copy-term-btn" title="نسخ المصطلح">
                        <i class="fas fa-copy"></i>
                    </button>
                    <button class="highlight-term-btn" title="تمييز في النص">
                        <i class="fas fa-highlighter"></i>
                    </button>
                </div>
            </div>
        `;
    }

    highlightTermsInContent() {
        // البحث عن المصطلحات في محتوى الصفحة وتمييزها
        const contentElements = document.querySelectorAll('.content-text p, .content-text li, .content-text td');
        
        contentElements.forEach(element => {
            let html = element.innerHTML;
            
            Object.keys(this.glossaryData).forEach(term => {
                const regex = new RegExp(`\\b${term}\\b`, 'gi');
                html = html.replace(regex, `<span class="glossary-term" data-term="${term}">${term}</span>`);
            });
            
            element.innerHTML = html;
        });
    }

    setupEventListeners() {
        // فتح/إغلاق المسرد
        document.addEventListener('click', (e) => {
            if (e.target.closest('.glossary-toggle-btn')) {
                this.toggleGlossary();
            }
            
            if (e.target.closest('.glossary-close-btn')) {
                this.closeGlossary();
            }
            
            // النقر على مصطلح في النص
            if (e.target.classList.contains('glossary-term')) {
                const term = e.target.dataset.term;
                this.showTermPopup(term, e.target);
            }
            
            // نسخ المصطلح
            if (e.target.closest('.copy-term-btn')) {
                const termElement = e.target.closest('.glossary-item');
                this.copyTerm(termElement.dataset.term);
            }
            
            // تمييز المصطلح في النص
            if (e.target.closest('.highlight-term-btn')) {
                const termElement = e.target.closest('.glossary-item');
                this.highlightTermInPage(termElement.dataset.term);
            }
        });

        // البحث في المسرد
        document.addEventListener('input', (e) => {
            if (e.target.id === 'glossary-search') {
                this.filterGlossary();
            }
        });

        // تصفية حسب الفئة
        document.addEventListener('change', (e) => {
            if (e.target.id === 'category-filter') {
                this.filterGlossary();
            }
        });

        // إغلاق المسرد عند النقر خارجه
        document.addEventListener('click', (e) => {
            const modal = document.querySelector('.glossary-modal');
            if (e.target === modal) {
                this.closeGlossary();
            }
        });

        // إغلاق النوافذ المنبثقة عند النقر في أي مكان
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.term-popup') && !e.target.classList.contains('glossary-term')) {
                this.closeAllPopups();
            }
        });
    }

    toggleGlossary() {
        const modal = document.querySelector('.glossary-modal');
        if (modal.style.display === 'flex') {
            this.closeGlossary();
        } else {
            this.openGlossary();
        }
    }

    openGlossary() {
        const modal = document.querySelector('.glossary-modal');
        modal.style.display = 'flex';
        setTimeout(() => modal.classList.add('active'), 10);
        
        // التركيز على حقل البحث
        const searchInput = document.getElementById('glossary-search');
        if (searchInput) {
            searchInput.focus();
        }
    }

    closeGlossary() {
        const modal = document.querySelector('.glossary-modal');
        modal.classList.remove('active');
        setTimeout(() => modal.style.display = 'none', 300);
    }

    showTermPopup(term, element) {
        this.closeAllPopups();
        
        const termData = this.glossaryData[term];
        if (!termData) return;

        const popup = document.createElement('div');
        popup.className = 'term-popup';
        popup.innerHTML = `
            <div class="popup-content">
                <div class="popup-header">
                    <strong>${termData.term}</strong>
                    <button class="popup-close">×</button>
                </div>
                <div class="popup-arabic">${termData.arabic}</div>
                <div class="popup-definition">${termData.definition}</div>
                <div class="popup-actions">
                    <button class="btn btn-sm btn-primary open-glossary-btn">
                        <i class="fas fa-external-link-alt"></i>
                        فتح في المسرد
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(popup);
        
        // تحديد موقع النافذة المنبثقة
        const rect = element.getBoundingClientRect();
        popup.style.top = `${rect.bottom + window.scrollY + 5}px`;
        popup.style.left = `${rect.left + window.scrollX}px`;
        
        // التأكد من أن النافذة ضمن حدود الشاشة
        setTimeout(() => {
            const popupRect = popup.getBoundingClientRect();
            if (popupRect.right > window.innerWidth) {
                popup.style.left = `${window.innerWidth - popupRect.width - 10}px`;
            }
            if (popupRect.bottom > window.innerHeight) {
                popup.style.top = `${rect.top + window.scrollY - popupRect.height - 5}px`;
            }
        }, 10);

        // إضافة مستمعي الأحداث للنافذة المنبثقة
        popup.querySelector('.popup-close').addEventListener('click', () => {
            popup.remove();
        });
        
        popup.querySelector('.open-glossary-btn').addEventListener('click', () => {
            this.openGlossary();
            this.searchTerm(term);
            popup.remove();
        });
    }

    closeAllPopups() {
        document.querySelectorAll('.term-popup').forEach(popup => popup.remove());
    }

    filterGlossary() {
        const searchTerm = document.getElementById('glossary-search').value.toLowerCase();
        const categoryFilter = document.getElementById('category-filter').value;
        
        let filteredData = {};
        
        Object.entries(this.glossaryData).forEach(([key, value]) => {
            const matchesSearch = !searchTerm || 
                value.term.toLowerCase().includes(searchTerm) ||
                value.arabic.toLowerCase().includes(searchTerm) ||
                value.definition.toLowerCase().includes(searchTerm);
                
            const matchesCategory = !categoryFilter || value.category === categoryFilter;
            
            if (matchesSearch && matchesCategory) {
                filteredData[key] = value;
            }
        });
        
        // تحديث القائمة
        const glossaryList = document.querySelector('.glossary-list');
        glossaryList.innerHTML = this.renderGlossaryItems(filteredData);
        
        // تحديث الإحصائيات
        const filteredCount = Object.keys(filteredData).length;
        const totalCount = Object.keys(this.glossaryData).length;
        
        const totalTermsSpan = document.querySelector('.total-terms');
        const filteredTermsSpan = document.querySelector('.filtered-terms');
        
        if (filteredCount < totalCount) {
            totalTermsSpan.style.display = 'none';
            filteredTermsSpan.style.display = 'inline';
            filteredTermsSpan.textContent = `${filteredCount} من ${totalCount} مصطلح`;
        } else {
            totalTermsSpan.style.display = 'inline';
            filteredTermsSpan.style.display = 'none';
        }
    }

    searchTerm(term) {
        const searchInput = document.getElementById('glossary-search');
        if (searchInput) {
            searchInput.value = term;
            this.filterGlossary();
        }
    }

    copyTerm(term) {
        const termData = this.glossaryData[term];
        if (!termData) return;
        
        const textToCopy = `${termData.term} (${termData.arabic}): ${termData.definition}`;
        
        navigator.clipboard.writeText(textToCopy).then(() => {
            // إظهار رسالة نجاح
            this.showNotification('تم نسخ المصطلح بنجاح');
        }).catch(() => {
            // طريقة بديلة للنسخ
            const textArea = document.createElement('textarea');
            textArea.value = textToCopy;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            this.showNotification('تم نسخ المصطلح بنجاح');
        });
    }

    highlightTermInPage(term) {
        // إزالة التمييز السابق
        document.querySelectorAll('.highlighted-term').forEach(el => {
            el.classList.remove('highlighted-term');
        });
        
        // تمييز المصطلح الجديد
        document.querySelectorAll(`[data-term="${term}"]`).forEach(el => {
            el.classList.add('highlighted-term');
        });
        
        // التمرير إلى أول ظهور للمصطلح
        const firstOccurrence = document.querySelector(`[data-term="${term}"]`);
        if (firstOccurrence) {
            firstOccurrence.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
        
        this.showNotification(`تم تمييز جميع ظهورات "${term}" في الصفحة`);
    }

    showNotification(message) {
        const notification = document.createElement('div');
        notification.className = 'glossary-notification';
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => notification.classList.add('show'), 100);
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }
}

// تهيئة نظام المسرد عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    new GlossarySystem();
});
