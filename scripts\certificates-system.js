/**
 * نظام الشهادات والإنجازات
 * Certificates and Achievements System
 */

class CertificatesSystem {
    constructor() {
        this.certificates = {
            part1: {
                id: 'part1_completion',
                title: 'شهادة إتمام الجزء الأول',
                subtitle: 'الأسس والمبادئ في معايير AAMI',
                description: 'تشهد هذه الشهادة بأن حاملها قد أتم بنجاح دراسة الجزء الأول من الدليل العملي لمعايير AAMI',
                requirements: {
                    chapters: ['chapter1', 'chapter2', 'chapter3'],
                    minQuizScore: 70,
                    minProgress: 70
                },
                color: '#4CAF50'
            },
            part2: {
                id: 'part2_completion',
                title: 'شهادة إتمام الجزء الثاني',
                subtitle: 'التطبيقات العملية لمعايير AAMI',
                description: 'تشهد هذه الشهادة بأن حاملها قد أتم بنجاح دراسة الجزء الثاني من الدليل العملي لمعايير AAMI',
                requirements: {
                    chapters: ['chapter4', 'chapter5', 'chapter6', 'chapter7'],
                    minQuizScore: 70,
                    minProgress: 70
                },
                color: '#2196F3'
            },
            part3: {
                id: 'part3_completion',
                title: 'شهادة إتمام الجزء الثالث',
                subtitle: 'المواضيع المتقدمة في معايير AAMI',
                description: 'تشهد هذه الشهادة بأن حاملها قد أتم بنجاح دراسة الجزء الثالث من الدليل العملي لمعايير AAMI',
                requirements: {
                    chapters: ['chapter8', 'chapter9', 'chapter10'],
                    minQuizScore: 70,
                    minProgress: 70
                },
                color: '#FF9800'
            },
            complete: {
                id: 'course_completion',
                title: 'شهادة إتمام الدليل الكامل',
                subtitle: 'الدليل العملي الشامل لمعايير AAMI للمهندسين السريريين',
                description: 'تشهد هذه الشهادة بأن حاملها قد أتم بنجاح دراسة الدليل العملي الكامل لمعايير AAMI وأصبح مؤهلاً لتطبيق هذه المعايير في البيئة السريرية',
                requirements: {
                    parts: ['part1', 'part2', 'part3'],
                    minOverallProgress: 80,
                    minAverageQuizScore: 75
                },
                color: '#9C27B0'
            }
        };
        
        this.badges = {
            first_steps: {
                id: 'first_steps',
                title: 'الخطوات الأولى',
                description: 'أكمل أول فصل في الدليل',
                icon: 'fas fa-baby',
                color: '#4CAF50'
            },
            quiz_master: {
                id: 'quiz_master',
                title: 'خبير الاختبارات',
                description: 'احصل على 90% أو أكثر في 3 اختبارات',
                icon: 'fas fa-medal',
                color: '#FFD700'
            },
            perfectionist: {
                id: 'perfectionist',
                title: 'الكمالي',
                description: 'احصل على 100% في اختبار',
                icon: 'fas fa-star',
                color: '#FF6B6B'
            },
            dedicated_learner: {
                id: 'dedicated_learner',
                title: 'المتعلم المثابر',
                description: 'اقض أكثر من 10 ساعات في التعلم',
                icon: 'fas fa-clock',
                color: '#3F51B5'
            },
            explorer: {
                id: 'explorer',
                title: 'المستكشف',
                description: 'زر جميع صفحات الدليل',
                icon: 'fas fa-compass',
                color: '#00BCD4'
            },
            checklist_champion: {
                id: 'checklist_champion',
                title: 'بطل قوائم الفحص',
                description: 'أكمل جميع قوائم الفحص التفاعلية',
                icon: 'fas fa-list-check',
                color: '#8BC34A'
            }
        };
        
        this.init();
    }

    init() {
        this.createCertificatesInterface();
        this.setupEventListeners();
        this.checkEligibility();
    }

    createCertificatesInterface() {
        // إنشاء زر الشهادات
        const certificatesButton = document.createElement('button');
        certificatesButton.className = 'certificates-btn';
        certificatesButton.innerHTML = `
            <i class="fas fa-certificate"></i>
            <span>الشهادات</span>
            <div class="certificates-indicator">
                <span class="available-count">0</span>
            </div>
        `;
        certificatesButton.title = 'عرض الشهادات والإنجازات';
        
        document.body.appendChild(certificatesButton);
        
        // إنشاء نافذة الشهادات
        this.createCertificatesModal();
    }

    createCertificatesModal() {
        const modal = document.createElement('div');
        modal.className = 'certificates-modal';
        modal.innerHTML = `
            <div class="certificates-modal-content">
                <div class="certificates-header">
                    <h3>
                        <i class="fas fa-certificate"></i>
                        الشهادات والإنجازات
                    </h3>
                    <button class="certificates-close-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="certificates-tabs">
                    <button class="cert-tab-btn active" data-tab="certificates">الشهادات</button>
                    <button class="cert-tab-btn" data-tab="badges">الشارات</button>
                    <button class="cert-tab-btn" data-tab="progress">التقدم</button>
                </div>
                
                <div class="certificates-content">
                    <div class="cert-tab-content active" id="certificates-tab">
                        ${this.createCertificatesTab()}
                    </div>
                    <div class="cert-tab-content" id="badges-tab">
                        ${this.createBadgesTab()}
                    </div>
                    <div class="cert-tab-content" id="progress-tab">
                        ${this.createProgressTab()}
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
    }

    createCertificatesTab() {
        let html = '<div class="certificates-grid">';
        
        Object.values(this.certificates).forEach(cert => {
            const isEligible = this.checkCertificateEligibility(cert);
            const isEarned = this.isCertificateEarned(cert.id);
            
            html += `
                <div class="certificate-card ${isEarned ? 'earned' : isEligible ? 'eligible' : 'locked'}">
                    <div class="certificate-preview" style="border-color: ${cert.color}">
                        <div class="certificate-icon" style="background: ${cert.color}">
                            <i class="fas fa-certificate"></i>
                        </div>
                        <div class="certificate-info">
                            <h4>${cert.title}</h4>
                            <p>${cert.subtitle}</p>
                            <div class="certificate-status">
                                ${isEarned ? 
                                    '<span class="status-earned"><i class="fas fa-check-circle"></i> تم الحصول عليها</span>' :
                                    isEligible ? 
                                        '<span class="status-eligible"><i class="fas fa-star"></i> مؤهل للحصول عليها</span>' :
                                        '<span class="status-locked"><i class="fas fa-lock"></i> غير متاح</span>'
                                }
                            </div>
                        </div>
                    </div>
                    
                    <div class="certificate-actions">
                        ${isEarned ? 
                            `<button class="btn btn-primary view-certificate-btn" data-cert="${cert.id}">
                                <i class="fas fa-eye"></i> عرض الشهادة
                            </button>
                            <button class="btn btn-secondary download-certificate-btn" data-cert="${cert.id}">
                                <i class="fas fa-download"></i> تحميل
                            </button>` :
                            isEligible ?
                                `<button class="btn btn-success claim-certificate-btn" data-cert="${cert.id}">
                                    <i class="fas fa-award"></i> الحصول على الشهادة
                                </button>` :
                                `<div class="requirements-info">
                                    <h5>المتطلبات:</h5>
                                    ${this.getCertificateRequirements(cert)}
                                </div>`
                        }
                    </div>
                </div>
            `;
        });
        
        html += '</div>';
        return html;
    }

    createBadgesTab() {
        let html = '<div class="badges-grid">';
        
        Object.values(this.badges).forEach(badge => {
            const isEarned = this.isBadgeEarned(badge.id);
            
            html += `
                <div class="badge-card ${isEarned ? 'earned' : 'locked'}">
                    <div class="badge-icon" style="background: ${isEarned ? badge.color : '#e0e0e0'}">
                        <i class="${badge.icon}"></i>
                    </div>
                    <div class="badge-info">
                        <h4>${badge.title}</h4>
                        <p>${badge.description}</p>
                        ${isEarned ? 
                            `<span class="earned-date">تم الحصول عليها: ${this.getBadgeEarnedDate(badge.id)}</span>` :
                            '<span class="not-earned">لم يتم الحصول عليها بعد</span>'
                        }
                    </div>
                </div>
            `;
        });
        
        html += '</div>';
        return html;
    }

    createProgressTab() {
        const progress = this.getDetailedProgress();
        
        return `
            <div class="progress-overview">
                <div class="overall-progress">
                    <h4>التقدم الإجمالي</h4>
                    <div class="progress-circle-container">
                        <div class="progress-circle" data-progress="${progress.overall}">
                            <span class="progress-percentage">${progress.overall}%</span>
                        </div>
                    </div>
                </div>
                
                <div class="parts-progress">
                    ${Object.entries(progress.parts).map(([partId, partProgress]) => `
                        <div class="part-progress-item">
                            <h5>${this.getPartTitle(partId)}</h5>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${partProgress}%"></div>
                            </div>
                            <span class="progress-text">${partProgress}%</span>
                        </div>
                    `).join('')}
                </div>
                
                <div class="achievements-summary">
                    <div class="summary-item">
                        <i class="fas fa-certificate"></i>
                        <div class="summary-content">
                            <span class="summary-number">${progress.certificates.earned}</span>
                            <span class="summary-label">شهادة محققة</span>
                        </div>
                    </div>
                    <div class="summary-item">
                        <i class="fas fa-medal"></i>
                        <div class="summary-content">
                            <span class="summary-number">${progress.badges.earned}</span>
                            <span class="summary-label">شارة محققة</span>
                        </div>
                    </div>
                    <div class="summary-item">
                        <i class="fas fa-clock"></i>
                        <div class="summary-content">
                            <span class="summary-number">${progress.timeSpent}</span>
                            <span class="summary-label">وقت التعلم</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    setupEventListeners() {
        document.addEventListener('click', (e) => {
            // فتح/إغلاق نافذة الشهادات
            if (e.target.closest('.certificates-btn')) {
                this.toggleCertificatesModal();
            }
            
            if (e.target.closest('.certificates-close-btn')) {
                this.closeCertificatesModal();
            }
            
            // تبديل التبويبات
            if (e.target.classList.contains('cert-tab-btn')) {
                this.switchCertificateTab(e.target.dataset.tab);
            }
            
            // الحصول على شهادة
            if (e.target.closest('.claim-certificate-btn')) {
                const certId = e.target.closest('.claim-certificate-btn').dataset.cert;
                this.claimCertificate(certId);
            }
            
            // عرض شهادة
            if (e.target.closest('.view-certificate-btn')) {
                const certId = e.target.closest('.view-certificate-btn').dataset.cert;
                this.viewCertificate(certId);
            }
            
            // تحميل شهادة
            if (e.target.closest('.download-certificate-btn')) {
                const certId = e.target.closest('.download-certificate-btn').dataset.cert;
                this.downloadCertificate(certId);
            }
        });

        // إغلاق عند النقر خارج النافذة
        document.addEventListener('click', (e) => {
            const modal = document.querySelector('.certificates-modal');
            if (e.target === modal) {
                this.closeCertificatesModal();
            }
        });

        // الاستماع لأحداث التقدم
        document.addEventListener('progress-updated', () => {
            this.checkEligibility();
            this.updateCertificatesInterface();
        });
    }

    checkEligibility() {
        // فحص الشهادات
        Object.values(this.certificates).forEach(cert => {
            if (!this.isCertificateEarned(cert.id) && this.checkCertificateEligibility(cert)) {
                this.showEligibilityNotification(cert);
            }
        });

        // فحص الشارات
        Object.values(this.badges).forEach(badge => {
            if (!this.isBadgeEarned(badge.id) && this.checkBadgeEligibility(badge)) {
                this.earnBadge(badge.id);
            }
        });
    }

    checkCertificateEligibility(cert) {
        if (!window.progressTracker) return false;
        
        const progress = window.progressTracker.userProgress;
        
        switch (cert.id) {
            case 'part1_completion':
                return this.checkPartCompletion('part1', cert.requirements);
            case 'part2_completion':
                return this.checkPartCompletion('part2', cert.requirements);
            case 'part3_completion':
                return this.checkPartCompletion('part3', cert.requirements);
            case 'course_completion':
                return this.checkCourseCompletion(cert.requirements);
            default:
                return false;
        }
    }

    checkPartCompletion(partId, requirements) {
        if (!window.progressTracker) return false;
        
        const partProgress = window.progressTracker.calculatePartProgress(partId);
        const quizzes = Object.values(window.progressTracker.userProgress.quizzes);
        const partQuizzes = quizzes.filter(q => q.partId === partId);
        const averageQuizScore = partQuizzes.length > 0 ? 
            partQuizzes.reduce((sum, q) => sum + q.percentage, 0) / partQuizzes.length : 0;
        
        return partProgress >= requirements.minProgress && 
               averageQuizScore >= requirements.minQuizScore;
    }

    checkCourseCompletion(requirements) {
        if (!window.progressTracker) return false;
        
        const overallProgress = window.progressTracker.calculateOverallProgress();
        const quizzes = Object.values(window.progressTracker.userProgress.quizzes);
        const averageQuizScore = quizzes.length > 0 ? 
            quizzes.reduce((sum, q) => sum + q.percentage, 0) / quizzes.length : 0;
        
        return overallProgress >= requirements.minOverallProgress && 
               averageQuizScore >= requirements.minAverageQuizScore;
    }

    checkBadgeEligibility(badge) {
        if (!window.progressTracker) return false;
        
        const progress = window.progressTracker.userProgress;
        
        switch (badge.id) {
            case 'first_steps':
                return Object.keys(progress.pages).length >= 1;
            
            case 'quiz_master':
                const highScores = Object.values(progress.quizzes).filter(q => q.percentage >= 90);
                return highScores.length >= 3;
            
            case 'perfectionist':
                return Object.values(progress.quizzes).some(q => q.percentage === 100);
            
            case 'dedicated_learner':
                return progress.user.totalTimeSpent >= 10 * 60 * 60 * 1000; // 10 ساعات
            
            case 'explorer':
                const totalPages = 10; // عدد الصفحات الإجمالي
                return Object.keys(progress.pages).length >= totalPages;
            
            case 'checklist_champion':
                const totalChecklists = 3; // عدد قوائم الفحص
                return Object.keys(progress.checklists).length >= totalChecklists;
            
            default:
                return false;
        }
    }

    claimCertificate(certId) {
        const cert = this.certificates[certId];
        if (!cert || !this.checkCertificateEligibility(cert)) return;
        
        // حفظ الشهادة في التخزين المحلي
        const earnedCertificates = this.getEarnedCertificates();
        earnedCertificates[certId] = {
            id: certId,
            earnedDate: new Date().toISOString(),
            recipientName: this.getRecipientName()
        };
        
        localStorage.setItem('aami_certificates', JSON.stringify(earnedCertificates));
        
        // إظهار رسالة تهنئة
        this.showCertificateEarnedNotification(cert);
        
        // تحديث الواجهة
        this.updateCertificatesInterface();
        this.refreshCertificatesContent();
    }

    viewCertificate(certId) {
        const cert = this.certificates[certId];
        const earnedCert = this.getEarnedCertificates()[certId];
        
        if (!cert || !earnedCert) return;
        
        this.createCertificateViewer(cert, earnedCert);
    }

    createCertificateViewer(cert, earnedCert) {
        const viewer = document.createElement('div');
        viewer.className = 'certificate-viewer';
        viewer.innerHTML = `
            <div class="certificate-viewer-content">
                <div class="certificate-viewer-header">
                    <h3>عرض الشهادة</h3>
                    <button class="certificate-viewer-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="certificate-display">
                    ${this.generateCertificateHTML(cert, earnedCert)}
                </div>
                
                <div class="certificate-viewer-actions">
                    <button class="btn btn-primary print-certificate-btn">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                    <button class="btn btn-secondary download-pdf-btn">
                        <i class="fas fa-file-pdf"></i> تحميل PDF
                    </button>
                    <button class="btn btn-secondary share-certificate-btn">
                        <i class="fas fa-share"></i> مشاركة
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(viewer);
        
        // إضافة مستمعي الأحداث
        viewer.querySelector('.certificate-viewer-close').addEventListener('click', () => {
            viewer.remove();
        });
        
        viewer.querySelector('.print-certificate-btn').addEventListener('click', () => {
            this.printCertificate(cert, earnedCert);
        });
        
        viewer.querySelector('.download-pdf-btn').addEventListener('click', () => {
            this.downloadCertificateAsPDF(cert, earnedCert);
        });
        
        viewer.querySelector('.share-certificate-btn').addEventListener('click', () => {
            this.shareCertificate(cert, earnedCert);
        });
        
        setTimeout(() => viewer.classList.add('active'), 10);
    }

    generateCertificateHTML(cert, earnedCert) {
        const earnedDate = new Date(earnedCert.earnedDate).toLocaleDateString('ar-SA');
        const recipientName = earnedCert.recipientName || 'المتعلم';
        
        return `
            <div class="certificate-document" style="border-color: ${cert.color}">
                <div class="certificate-header">
                    <div class="certificate-logo">
                        <i class="fas fa-certificate" style="color: ${cert.color}"></i>
                    </div>
                    <h1 style="color: ${cert.color}">شهادة إتمام</h1>
                    <div class="certificate-subtitle">${cert.subtitle}</div>
                </div>
                
                <div class="certificate-body">
                    <div class="certificate-text">
                        <p class="certificate-intro">تشهد هذه الشهادة بأن</p>
                        <h2 class="recipient-name">${recipientName}</h2>
                        <p class="certificate-description">${cert.description}</p>
                    </div>
                    
                    <div class="certificate-details">
                        <div class="detail-item">
                            <span class="detail-label">تاريخ الإنجاز:</span>
                            <span class="detail-value">${earnedDate}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">رقم الشهادة:</span>
                            <span class="detail-value">${this.generateCertificateNumber(cert.id, earnedCert.earnedDate)}</span>
                        </div>
                    </div>
                </div>
                
                <div class="certificate-footer">
                    <div class="certificate-signature">
                        <div class="signature-line"></div>
                        <p>الدليل العملي لمعايير AAMI</p>
                        <p>للمهندسين السريريين</p>
                    </div>
                    
                    <div class="certificate-seal" style="border-color: ${cert.color}">
                        <i class="fas fa-award" style="color: ${cert.color}"></i>
                    </div>
                </div>
            </div>
        `;
    }

    earnBadge(badgeId) {
        const earnedBadges = this.getEarnedBadges();
        if (earnedBadges[badgeId]) return; // الشارة محققة مسبقاً
        
        earnedBadges[badgeId] = {
            id: badgeId,
            earnedDate: new Date().toISOString()
        };
        
        localStorage.setItem('aami_badges', JSON.stringify(earnedBadges));
        
        const badge = this.badges[badgeId];
        this.showBadgeEarnedNotification(badge);
        
        this.updateCertificatesInterface();
    }

    getEarnedCertificates() {
        try {
            return JSON.parse(localStorage.getItem('aami_certificates') || '{}');
        } catch {
            return {};
        }
    }

    getEarnedBadges() {
        try {
            return JSON.parse(localStorage.getItem('aami_badges') || '{}');
        } catch {
            return {};
        }
    }

    isCertificateEarned(certId) {
        return !!this.getEarnedCertificates()[certId];
    }

    isBadgeEarned(badgeId) {
        return !!this.getEarnedBadges()[badgeId];
    }

    getBadgeEarnedDate(badgeId) {
        const badge = this.getEarnedBadges()[badgeId];
        return badge ? new Date(badge.earnedDate).toLocaleDateString('ar-SA') : '';
    }

    getRecipientName() {
        // يمكن تحسين هذا ليطلب من المستخدم إدخال اسمه
        return prompt('أدخل اسمك للشهادة:') || 'المتعلم';
    }

    generateCertificateNumber(certId, earnedDate) {
        const date = new Date(earnedDate);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hash = certId.substring(0, 4).toUpperCase();
        
        return `AAMI-${year}${month}${day}-${hash}`;
    }

    updateCertificatesInterface() {
        const btn = document.querySelector('.certificates-btn');
        if (btn) {
            const availableCount = this.getAvailableCertificatesCount();
            const indicator = btn.querySelector('.available-count');
            if (indicator) {
                indicator.textContent = availableCount;
                indicator.style.display = availableCount > 0 ? 'block' : 'none';
            }
        }
    }

    getAvailableCertificatesCount() {
        return Object.values(this.certificates).filter(cert => 
            !this.isCertificateEarned(cert.id) && this.checkCertificateEligibility(cert)
        ).length;
    }

    refreshCertificatesContent() {
        const certificatesTab = document.getElementById('certificates-tab');
        const badgesTab = document.getElementById('badges-tab');
        const progressTab = document.getElementById('progress-tab');
        
        if (certificatesTab) certificatesTab.innerHTML = this.createCertificatesTab();
        if (badgesTab) badgesTab.innerHTML = this.createBadgesTab();
        if (progressTab) progressTab.innerHTML = this.createProgressTab();
    }

    toggleCertificatesModal() {
        const modal = document.querySelector('.certificates-modal');
        if (modal.style.display === 'flex') {
            this.closeCertificatesModal();
        } else {
            this.openCertificatesModal();
        }
    }

    openCertificatesModal() {
        const modal = document.querySelector('.certificates-modal');
        modal.style.display = 'flex';
        setTimeout(() => modal.classList.add('active'), 10);
        this.refreshCertificatesContent();
    }

    closeCertificatesModal() {
        const modal = document.querySelector('.certificates-modal');
        modal.classList.remove('active');
        setTimeout(() => modal.style.display = 'none', 300);
    }

    switchCertificateTab(tabName) {
        document.querySelectorAll('.cert-tab-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.cert-tab-content').forEach(content => content.classList.remove('active'));
        
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        document.querySelector(`#${tabName}-tab`).classList.add('active');
    }

    showEligibilityNotification(cert) {
        const notification = document.createElement('div');
        notification.className = 'eligibility-notification';
        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-icon">
                    <i class="fas fa-certificate"></i>
                </div>
                <div class="notification-text">
                    <h4>شهادة متاحة!</h4>
                    <p>أصبحت مؤهلاً للحصول على: ${cert.title}</p>
                    <button class="btn btn-sm btn-primary claim-now-btn" data-cert="${cert.id}">
                        احصل عليها الآن
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        notification.querySelector('.claim-now-btn').addEventListener('click', () => {
            this.claimCertificate(cert.id);
            notification.remove();
        });
        
        setTimeout(() => notification.classList.add('show'), 100);
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 500);
        }, 8000);
    }

    showCertificateEarnedNotification(cert) {
        const notification = document.createElement('div');
        notification.className = 'certificate-earned-notification';
        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-animation">
                    <i class="fas fa-certificate"></i>
                </div>
                <div class="notification-text">
                    <h3>تهانينا!</h3>
                    <h4>حصلت على شهادة جديدة</h4>
                    <p>${cert.title}</p>
                </div>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => notification.classList.add('show'), 100);
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 500);
        }, 6000);
    }

    showBadgeEarnedNotification(badge) {
        const notification = document.createElement('div');
        notification.className = 'badge-earned-notification';
        notification.innerHTML = `
            <div class="notification-content">
                <div class="badge-icon" style="background: ${badge.color}">
                    <i class="${badge.icon}"></i>
                </div>
                <div class="notification-text">
                    <h4>شارة جديدة!</h4>
                    <h5>${badge.title}</h5>
                    <p>${badge.description}</p>
                </div>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => notification.classList.add('show'), 100);
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 500);
        }, 5000);
    }

    getCertificateRequirements(cert) {
        const requirements = cert.requirements;
        let html = '<ul>';
        
        if (requirements.chapters) {
            html += `<li>إكمال الفصول: ${requirements.chapters.length}</li>`;
        }
        
        if (requirements.parts) {
            html += `<li>إكمال الأجزاء: ${requirements.parts.length}</li>`;
        }
        
        if (requirements.minProgress) {
            html += `<li>تقدم لا يقل عن: ${requirements.minProgress}%</li>`;
        }
        
        if (requirements.minQuizScore) {
            html += `<li>متوسط نتائج الاختبارات: ${requirements.minQuizScore}%</li>`;
        }
        
        if (requirements.minOverallProgress) {
            html += `<li>التقدم الإجمالي: ${requirements.minOverallProgress}%</li>`;
        }
        
        if (requirements.minAverageQuizScore) {
            html += `<li>متوسط جميع الاختبارات: ${requirements.minAverageQuizScore}%</li>`;
        }
        
        html += '</ul>';
        return html;
    }

    getDetailedProgress() {
        if (!window.progressTracker) {
            return {
                overall: 0,
                parts: { part1: 0, part2: 0, part3: 0 },
                certificates: { earned: 0, total: Object.keys(this.certificates).length },
                badges: { earned: 0, total: Object.keys(this.badges).length },
                timeSpent: '0 دقيقة'
            };
        }
        
        const progress = window.progressTracker.userProgress;
        const earnedCertificates = Object.keys(this.getEarnedCertificates()).length;
        const earnedBadges = Object.keys(this.getEarnedBadges()).length;
        
        return {
            overall: window.progressTracker.calculateOverallProgress(),
            parts: {
                part1: window.progressTracker.calculatePartProgress('part1'),
                part2: window.progressTracker.calculatePartProgress('part2'),
                part3: window.progressTracker.calculatePartProgress('part3')
            },
            certificates: { 
                earned: earnedCertificates, 
                total: Object.keys(this.certificates).length 
            },
            badges: { 
                earned: earnedBadges, 
                total: Object.keys(this.badges).length 
            },
            timeSpent: window.progressTracker.formatTime(progress.user.totalTimeSpent)
        };
    }

    getPartTitle(partId) {
        const titles = {
            part1: 'الجزء الأول: الأسس والمبادئ',
            part2: 'الجزء الثاني: التطبيقات العملية',
            part3: 'الجزء الثالث: المواضيع المتقدمة'
        };
        return titles[partId] || partId;
    }

    printCertificate(cert, earnedCert) {
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>${cert.title}</title>
                <style>
                    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; }
                    .certificate-document { max-width: 800px; margin: 0 auto; }
                    /* إضافة أنماط الطباعة هنا */
                </style>
            </head>
            <body>
                ${this.generateCertificateHTML(cert, earnedCert)}
            </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }

    downloadCertificateAsPDF(cert, earnedCert) {
        // يمكن تطوير هذا باستخدام مكتبة PDF مثل jsPDF
        alert('ميزة تحميل PDF ستكون متاحة قريباً');
    }

    shareCertificate(cert, earnedCert) {
        const text = `حصلت على شهادة: ${cert.title} من الدليل العملي لمعايير AAMI`;
        
        if (navigator.share) {
            navigator.share({
                title: cert.title,
                text: text,
                url: window.location.href
            });
        } else {
            // نسخ النص للحافظة
            navigator.clipboard.writeText(text).then(() => {
                alert('تم نسخ النص للمشاركة');
            });
        }
    }

    downloadCertificate(certId) {
        const cert = this.certificates[certId];
        const earnedCert = this.getEarnedCertificates()[certId];
        
        if (!cert || !earnedCert) return;
        
        // إنشاء ملف HTML للشهادة
        const certificateHTML = `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>${cert.title}</title>
                <style>
                    body { 
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
                        margin: 0; 
                        padding: 20px; 
                        background: #f5f5f5;
                    }
                    .certificate-document { 
                        max-width: 800px; 
                        margin: 0 auto; 
                        background: white; 
                        padding: 40px; 
                        border-radius: 12px; 
                        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
                    }
                    /* إضافة المزيد من الأنماط */
                </style>
            </head>
            <body>
                ${this.generateCertificateHTML(cert, earnedCert)}
            </body>
            </html>
        `;
        
        const blob = new Blob([certificateHTML], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${cert.title.replace(/\s+/g, '_')}.html`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
}

// تهيئة نظام الشهادات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.certificatesSystem = new CertificatesSystem();
});
