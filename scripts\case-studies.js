/**
 * نظام دراسات الحالة التفاعلية
 * Interactive Case Studies System
 */

class CaseStudiesSystem {
    constructor() {
        this.caseStudies = {
            part1: {
                title: 'دراسة حالة: تطبيق معايير AAMI في مستشفى الملك فيصل',
                description: 'كيف نجح مستشفى الملك فيصل في تطبيق معايير AAMI لتحسين جودة الرعاية',
                scenario: `
                    مستشفى الملك فيصل التخصصي ومركز الأبحاث قرر تطبيق معايير AAMI الشاملة 
                    لتحسين إدارة الأجهزة الطبية وضمان أعلى مستويات السلامة للمرضى.
                `,
                challenges: [
                    'تنوع الأجهزة الطبية من مصنعين مختلفين',
                    'نقص في الكوادر المدربة على معايير AAMI',
                    'الحاجة لتحديث الإجراءات الحالية',
                    'ضرورة التدريب المستمر للفرق الطبية'
                ],
                solutions: [
                    'إنشاء قسم متخصص للهندسة السريرية',
                    'تطوير برنامج تدريبي شامل على معايير AAMI',
                    'تطبيق نظام إدارة الأجهزة الطبية المحوسب',
                    'إجراء تدقيق دوري للامتثال للمعايير'
                ],
                outcomes: [
                    'تحسن بنسبة 40% في معدلات السلامة',
                    'تقليل أعطال الأجهزة بنسبة 35%',
                    'الحصول على اعتماد JCI',
                    'توفير 25% من تكاليف الصيانة'
                ],
                lessons: [
                    'أهمية التخطيط المسبق والتدريج في التطبيق',
                    'ضرورة إشراك جميع الأطراف المعنية',
                    'قيمة الاستثمار في التدريب والتطوير',
                    'أهمية المراجعة والتحسين المستمر'
                ]
            },
            part2: {
                title: 'دراسة حالة: إدارة المخاطر في وحدة العناية المركزة',
                description: 'تطبيق معيار ISO 14971 لإدارة مخاطر أجهزة التنفس الصناعي',
                scenario: `
                    وحدة العناية المركزة في مستشفى الملك عبدالعزيز واجهت تحديات في إدارة 
                    مخاطر أجهزة التنفس الصناعي، خاصة مع زيادة الحاجة أثناء جائحة كوفيد-19.
                `,
                challenges: [
                    'زيادة الطلب على أجهزة التنفس الصناعي',
                    'تنوع أنواع الأجهزة المستخدمة',
                    'ضرورة التشغيل المستمر دون توقف',
                    'الحاجة لتدريب سريع للكوادر الجديدة'
                ],
                solutions: [
                    'تطبيق منهجية ISO 14971 لتقييم المخاطر',
                    'إنشاء مصفوفة مخاطر شاملة',
                    'تطوير إجراءات طوارئ محددة',
                    'تنفيذ برنامج صيانة وقائية مكثف'
                ],
                outcomes: [
                    'تقليل حوادث الأجهزة بنسبة 60%',
                    'تحسين وقت الاستجابة للطوارئ',
                    'زيادة معدل توفر الأجهزة إلى 99.5%',
                    'تحسين رضا الفرق الطبية'
                ],
                lessons: [
                    'أهمية التقييم المستمر للمخاطر',
                    'قيمة التدريب العملي على السيناريوهات',
                    'ضرورة وجود خطط بديلة',
                    'أهمية التوثيق الدقيق للحوادث'
                ]
            },
            part3: {
                title: 'دراسة حالة: الأمن السيبراني في المستشفى الرقمي',
                description: 'تأمين الأجهزة الطبية المتصلة في مستشفى الملك فهد الجامعي',
                scenario: `
                    مستشفى الملك فهد الجامعي بدأ مشروع التحول الرقمي الشامل، مما تطلب 
                    تأمين شبكة من الأجهزة الطبية المتصلة وحماية بيانات المرضى.
                `,
                challenges: [
                    'تأمين أكثر من 500 جهاز طبي متصل',
                    'حماية بيانات أكثر من 10,000 مريض',
                    'ضمان استمرارية الخدمة 24/7',
                    'الامتثال لقوانين حماية البيانات'
                ],
                solutions: [
                    'تطبيق معيار AAMI TIR57 للأمن السيبراني',
                    'إنشاء شبكة منفصلة للأجهزة الطبية',
                    'تطوير نظام مراقبة أمنية متقدم',
                    'تدريب الفرق على الوعي الأمني'
                ],
                outcomes: [
                    'صفر حوادث أمنية خطيرة',
                    'تحسين كفاءة تبادل البيانات',
                    'الحصول على شهادة ISO 27001',
                    'توفير 30% من تكاليف إدارة الشبكة'
                ],
                lessons: [
                    'أهمية التخطيط الأمني من البداية',
                    'ضرورة التوازن بين الأمان والوظائف',
                    'قيمة التدريب المستمر على الأمان',
                    'أهمية المراجعة الدورية للثغرات'
                ]
            }
        };
        
        this.init();
    }

    init() {
        this.createCaseStudyInterfaces();
        this.setupEventListeners();
    }

    createCaseStudyInterfaces() {
        // إضافة دراسات الحالة للأجزاء المناسبة
        this.addCaseStudyToSection('part1', 'pages/part1/index.html');
        this.addCaseStudyToSection('part2', 'pages/part2/index.html');
        this.addCaseStudyToSection('part3', 'pages/part3/index.html');
    }

    addCaseStudyToSection(partId, pageUrl) {
        // التحقق من وجود الصفحة المناسبة
        const currentUrl = window.location.pathname;
        if (!currentUrl.includes(pageUrl.split('/').pop())) return;

        const caseStudy = this.caseStudies[partId];
        const container = document.querySelector('.chapter-content .container');
        
        if (!container) return;

        const caseStudyDiv = document.createElement('div');
        caseStudyDiv.className = 'case-study-container';
        caseStudyDiv.innerHTML = this.createCaseStudyHTML(partId, caseStudy);
        
        container.appendChild(caseStudyDiv);
    }

    createCaseStudyHTML(partId, caseStudy) {
        return `
            <div class="case-study-section" data-case="${partId}">
                <div class="case-study-header">
                    <h3>
                        <i class="fas fa-microscope"></i>
                        ${caseStudy.title}
                    </h3>
                    <p class="case-study-description">${caseStudy.description}</p>
                    <button class="btn btn-primary case-study-start-btn">
                        <i class="fas fa-play"></i>
                        ابدأ دراسة الحالة
                    </button>
                </div>
            </div>
        `;
    }

    setupEventListeners() {
        document.addEventListener('click', (e) => {
            if (e.target.closest('.case-study-start-btn')) {
                const caseStudySection = e.target.closest('.case-study-section');
                const caseId = caseStudySection.dataset.case;
                this.openCaseStudy(caseId);
            }
        });
    }

    openCaseStudy(caseId) {
        const caseStudy = this.caseStudies[caseId];
        if (!caseStudy) return;

        this.createCaseStudyModal(caseId, caseStudy);
    }

    createCaseStudyModal(caseId, caseStudy) {
        // إزالة أي modal موجود
        const existingModal = document.querySelector('.case-study-modal');
        if (existingModal) {
            existingModal.remove();
        }

        const modal = document.createElement('div');
        modal.className = 'case-study-modal';
        modal.innerHTML = `
            <div class="case-study-modal-content">
                <div class="case-study-modal-header">
                    <h3>${caseStudy.title}</h3>
                    <button class="case-study-close-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="case-study-progress">
                    <div class="progress-steps">
                        <div class="step active" data-step="scenario">
                            <span class="step-number">1</span>
                            <span class="step-label">السيناريو</span>
                        </div>
                        <div class="step" data-step="challenges">
                            <span class="step-number">2</span>
                            <span class="step-label">التحديات</span>
                        </div>
                        <div class="step" data-step="solutions">
                            <span class="step-number">3</span>
                            <span class="step-label">الحلول</span>
                        </div>
                        <div class="step" data-step="outcomes">
                            <span class="step-number">4</span>
                            <span class="step-label">النتائج</span>
                        </div>
                        <div class="step" data-step="lessons">
                            <span class="step-number">5</span>
                            <span class="step-label">الدروس المستفادة</span>
                        </div>
                    </div>
                </div>
                
                <div class="case-study-content">
                    ${this.renderCaseStudyStep('scenario', caseStudy)}
                </div>
                
                <div class="case-study-navigation">
                    <button class="btn btn-secondary case-study-prev-btn" disabled>
                        <i class="fas fa-arrow-right"></i>
                        السابق
                    </button>
                    <button class="btn btn-primary case-study-next-btn">
                        التالي
                        <i class="fas fa-arrow-left"></i>
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // إضافة مستمعي الأحداث
        this.setupModalEventListeners(modal, caseStudy);
        
        // إظهار المودال
        setTimeout(() => modal.classList.add('active'), 10);
    }

    setupModalEventListeners(modal, caseStudy) {
        const closeBtn = modal.querySelector('.case-study-close-btn');
        const prevBtn = modal.querySelector('.case-study-prev-btn');
        const nextBtn = modal.querySelector('.case-study-next-btn');
        const steps = modal.querySelectorAll('.step');

        let currentStep = 0;
        const stepNames = ['scenario', 'challenges', 'solutions', 'outcomes', 'lessons'];

        closeBtn.addEventListener('click', () => this.closeCaseStudy(modal));
        
        prevBtn.addEventListener('click', () => {
            if (currentStep > 0) {
                currentStep--;
                this.updateCaseStudyStep(modal, caseStudy, stepNames[currentStep], currentStep);
            }
        });

        nextBtn.addEventListener('click', () => {
            if (currentStep < stepNames.length - 1) {
                currentStep++;
                this.updateCaseStudyStep(modal, caseStudy, stepNames[currentStep], currentStep);
            } else {
                this.completeCaseStudy(modal);
            }
        });

        // النقر على الخطوات للانتقال المباشر
        steps.forEach((step, index) => {
            step.addEventListener('click', () => {
                currentStep = index;
                this.updateCaseStudyStep(modal, caseStudy, stepNames[currentStep], currentStep);
            });
        });

        // إغلاق عند النقر خارج المحتوى
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeCaseStudy(modal);
            }
        });
    }

    updateCaseStudyStep(modal, caseStudy, stepName, stepIndex) {
        const content = modal.querySelector('.case-study-content');
        const steps = modal.querySelectorAll('.step');
        const prevBtn = modal.querySelector('.case-study-prev-btn');
        const nextBtn = modal.querySelector('.case-study-next-btn');

        // تحديث المحتوى
        content.innerHTML = this.renderCaseStudyStep(stepName, caseStudy);

        // تحديث الخطوات
        steps.forEach((step, index) => {
            step.classList.toggle('active', index === stepIndex);
            step.classList.toggle('completed', index < stepIndex);
        });

        // تحديث أزرار التنقل
        prevBtn.disabled = stepIndex === 0;
        
        if (stepIndex === steps.length - 1) {
            nextBtn.innerHTML = '<i class="fas fa-check"></i> إنهاء دراسة الحالة';
        } else {
            nextBtn.innerHTML = 'التالي <i class="fas fa-arrow-left"></i>';
        }
    }

    renderCaseStudyStep(stepName, caseStudy) {
        switch (stepName) {
            case 'scenario':
                return `
                    <div class="case-study-step scenario-step">
                        <h4>
                            <i class="fas fa-hospital"></i>
                            السيناريو
                        </h4>
                        <div class="scenario-content">
                            <p>${caseStudy.scenario}</p>
                        </div>
                    </div>
                `;
            
            case 'challenges':
                return `
                    <div class="case-study-step challenges-step">
                        <h4>
                            <i class="fas fa-exclamation-triangle"></i>
                            التحديات المواجهة
                        </h4>
                        <div class="challenges-list">
                            ${caseStudy.challenges.map((challenge, index) => `
                                <div class="challenge-item">
                                    <span class="challenge-number">${index + 1}</span>
                                    <span class="challenge-text">${challenge}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            
            case 'solutions':
                return `
                    <div class="case-study-step solutions-step">
                        <h4>
                            <i class="fas fa-lightbulb"></i>
                            الحلول المطبقة
                        </h4>
                        <div class="solutions-list">
                            ${caseStudy.solutions.map((solution, index) => `
                                <div class="solution-item">
                                    <span class="solution-number">${index + 1}</span>
                                    <span class="solution-text">${solution}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            
            case 'outcomes':
                return `
                    <div class="case-study-step outcomes-step">
                        <h4>
                            <i class="fas fa-chart-line"></i>
                            النتائج المحققة
                        </h4>
                        <div class="outcomes-list">
                            ${caseStudy.outcomes.map((outcome, index) => `
                                <div class="outcome-item">
                                    <i class="fas fa-check-circle"></i>
                                    <span class="outcome-text">${outcome}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            
            case 'lessons':
                return `
                    <div class="case-study-step lessons-step">
                        <h4>
                            <i class="fas fa-graduation-cap"></i>
                            الدروس المستفادة
                        </h4>
                        <div class="lessons-list">
                            ${caseStudy.lessons.map((lesson, index) => `
                                <div class="lesson-item">
                                    <span class="lesson-number">${index + 1}</span>
                                    <span class="lesson-text">${lesson}</span>
                                </div>
                            `).join('')}
                        </div>
                        <div class="case-study-completion">
                            <div class="completion-message">
                                <i class="fas fa-trophy"></i>
                                <h5>تهانينا! لقد أكملت دراسة الحالة بنجاح</h5>
                                <p>يمكنك الآن تطبيق هذه المعرفة في بيئة عملك</p>
                            </div>
                        </div>
                    </div>
                `;
            
            default:
                return '<p>محتوى غير متوفر</p>';
        }
    }

    completeCaseStudy(modal) {
        // إظهار رسالة إكمال
        const content = modal.querySelector('.case-study-content');
        content.innerHTML = `
            <div class="case-study-completion-full">
                <div class="completion-animation">
                    <i class="fas fa-trophy"></i>
                </div>
                <h3>تم إكمال دراسة الحالة بنجاح!</h3>
                <p>لقد تعلمت كيفية تطبيق معايير AAMI في بيئة عملية حقيقية</p>
                <div class="completion-actions">
                    <button class="btn btn-primary restart-case-btn">
                        <i class="fas fa-redo"></i>
                        إعادة دراسة الحالة
                    </button>
                    <button class="btn btn-secondary close-case-btn">
                        <i class="fas fa-times"></i>
                        إغلاق
                    </button>
                </div>
            </div>
        `;

        // إخفاء أزرار التنقل
        const navigation = modal.querySelector('.case-study-navigation');
        navigation.style.display = 'none';

        // إضافة مستمعي الأحداث للأزرار الجديدة
        content.querySelector('.restart-case-btn').addEventListener('click', () => {
            location.reload();
        });

        content.querySelector('.close-case-btn').addEventListener('click', () => {
            this.closeCaseStudy(modal);
        });
    }

    closeCaseStudy(modal) {
        modal.classList.remove('active');
        setTimeout(() => modal.remove(), 300);
    }
}

// تهيئة نظام دراسات الحالة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    new CaseStudiesSystem();
});
