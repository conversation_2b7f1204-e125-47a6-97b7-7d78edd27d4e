/**
 * نظام الإشارات المرجعية
 * Bookmarks System
 */

class BookmarksSystem {
    constructor() {
        this.storageKey = 'aami_bookmarks';
        this.bookmarks = this.loadBookmarks();
        this.currentPage = this.getCurrentPageInfo();
        
        this.init();
    }

    init() {
        this.createBookmarksInterface();
        this.addBookmarkButtons();
        this.setupEventListeners();
        this.updateBookmarkStatus();
    }

    loadBookmarks() {
        try {
            return JSON.parse(localStorage.getItem(this.storageKey) || '[]');
        } catch (error) {
            console.warn('خطأ في تحميل الإشارات المرجعية:', error);
            return [];
        }
    }

    saveBookmarks() {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(this.bookmarks));
        } catch (error) {
            console.warn('خطأ في حفظ الإشارات المرجعية:', error);
        }
    }

    getCurrentPageInfo() {
        const path = window.location.pathname;
        const title = document.title;
        const url = window.location.href;
        
        // استخراج معلومات الصفحة
        let pageType = 'page';
        let partId = '';
        let chapterId = '';
        
        const match = path.match(/\/(part\d+)\/(chapter\d+)\.html/);
        if (match) {
            pageType = 'chapter';
            partId = match[1];
            chapterId = match[2];
        }
        
        return {
            url,
            title,
            path,
            pageType,
            partId,
            chapterId,
            timestamp: new Date().toISOString()
        };
    }

    createBookmarksInterface() {
        // إنشاء زر الإشارات المرجعية
        const bookmarksButton = document.createElement('button');
        bookmarksButton.className = 'bookmarks-btn';
        bookmarksButton.innerHTML = `
            <i class="fas fa-bookmark"></i>
            <span>الإشارات المرجعية</span>
            <div class="bookmarks-count">
                <span class="count-number">${this.bookmarks.length}</span>
            </div>
        `;
        bookmarksButton.title = 'عرض الإشارات المرجعية المحفوظة';
        
        document.body.appendChild(bookmarksButton);
        
        // إنشاء نافذة الإشارات المرجعية
        this.createBookmarksModal();
    }

    createBookmarksModal() {
        const modal = document.createElement('div');
        modal.className = 'bookmarks-modal';
        modal.innerHTML = `
            <div class="bookmarks-modal-content">
                <div class="bookmarks-header">
                    <h3>
                        <i class="fas fa-bookmark"></i>
                        الإشارات المرجعية المحفوظة
                    </h3>
                    <button class="bookmarks-close-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="bookmarks-toolbar">
                    <div class="bookmarks-search">
                        <i class="fas fa-search"></i>
                        <input type="text" id="bookmarks-search" placeholder="ابحث في الإشارات المرجعية...">
                    </div>
                    <div class="bookmarks-actions">
                        <button class="btn btn-secondary export-bookmarks-btn">
                            <i class="fas fa-download"></i>
                            تصدير
                        </button>
                        <button class="btn btn-secondary import-bookmarks-btn">
                            <i class="fas fa-upload"></i>
                            استيراد
                        </button>
                        <button class="btn btn-danger clear-bookmarks-btn">
                            <i class="fas fa-trash"></i>
                            مسح الكل
                        </button>
                    </div>
                </div>
                
                <div class="bookmarks-content">
                    <div class="bookmarks-stats">
                        <div class="stat-item">
                            <i class="fas fa-bookmark"></i>
                            <span class="stat-number">${this.bookmarks.length}</span>
                            <span class="stat-label">إشارة مرجعية</span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-calendar"></i>
                            <span class="stat-number">${this.getBookmarksThisWeek()}</span>
                            <span class="stat-label">هذا الأسبوع</span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-folder"></i>
                            <span class="stat-number">${this.getUniqueCategories().length}</span>
                            <span class="stat-label">فئة</span>
                        </div>
                    </div>
                    
                    <div class="bookmarks-list" id="bookmarks-list">
                        ${this.renderBookmarksList()}
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
    }

    addBookmarkButtons() {
        // إضافة أزرار الإشارة المرجعية للأقسام
        const sections = document.querySelectorAll('.content-section');
        sections.forEach((section, index) => {
            const sectionId = section.id || `section-${index}`;
            const sectionTitle = section.querySelector('h2, h3, h4')?.textContent || `القسم ${index + 1}`;
            
            const bookmarkBtn = document.createElement('button');
            bookmarkBtn.className = 'section-bookmark-btn';
            bookmarkBtn.innerHTML = '<i class="fas fa-bookmark"></i>';
            bookmarkBtn.title = 'إضافة إشارة مرجعية لهذا القسم';
            bookmarkBtn.dataset.sectionId = sectionId;
            bookmarkBtn.dataset.sectionTitle = sectionTitle;
            
            // إضافة الزر في أعلى القسم
            const sectionHeader = section.querySelector('h2, h3, h4');
            if (sectionHeader) {
                sectionHeader.style.position = 'relative';
                sectionHeader.appendChild(bookmarkBtn);
            }
        });

        // إضافة زر الإشارة المرجعية للصفحة الحالية
        this.addPageBookmarkButton();
    }

    addPageBookmarkButton() {
        const pageBookmarkBtn = document.createElement('button');
        pageBookmarkBtn.className = 'page-bookmark-btn';
        pageBookmarkBtn.innerHTML = `
            <i class="fas fa-bookmark"></i>
            <span>حفظ الصفحة</span>
        `;
        pageBookmarkBtn.title = 'إضافة إشارة مرجعية لهذه الصفحة';
        
        // إضافة الزر في الشريط العلوي أو الجانبي
        const header = document.querySelector('.header-content, .main-nav, .page-header');
        if (header) {
            header.appendChild(pageBookmarkBtn);
        } else {
            document.body.appendChild(pageBookmarkBtn);
        }
    }

    setupEventListeners() {
        // فتح/إغلاق نافذة الإشارات المرجعية
        document.addEventListener('click', (e) => {
            if (e.target.closest('.bookmarks-btn')) {
                this.toggleBookmarksModal();
            }
            
            if (e.target.closest('.bookmarks-close-btn')) {
                this.closeBookmarksModal();
            }
            
            // إضافة إشارة مرجعية للصفحة
            if (e.target.closest('.page-bookmark-btn')) {
                this.togglePageBookmark();
            }
            
            // إضافة إشارة مرجعية للقسم
            if (e.target.closest('.section-bookmark-btn')) {
                const btn = e.target.closest('.section-bookmark-btn');
                const sectionId = btn.dataset.sectionId;
                const sectionTitle = btn.dataset.sectionTitle;
                this.toggleSectionBookmark(sectionId, sectionTitle);
            }
            
            // حذف إشارة مرجعية
            if (e.target.closest('.delete-bookmark-btn')) {
                const bookmarkId = e.target.closest('.bookmark-item').dataset.bookmarkId;
                this.deleteBookmark(bookmarkId);
            }
            
            // تصدير الإشارات المرجعية
            if (e.target.closest('.export-bookmarks-btn')) {
                this.exportBookmarks();
            }
            
            // استيراد الإشارات المرجعية
            if (e.target.closest('.import-bookmarks-btn')) {
                this.importBookmarks();
            }
            
            // مسح جميع الإشارات المرجعية
            if (e.target.closest('.clear-bookmarks-btn')) {
                this.clearAllBookmarks();
            }
        });

        // البحث في الإشارات المرجعية
        document.addEventListener('input', (e) => {
            if (e.target.id === 'bookmarks-search') {
                this.filterBookmarks(e.target.value);
            }
        });

        // إغلاق النافذة عند النقر خارجها
        document.addEventListener('click', (e) => {
            const modal = document.querySelector('.bookmarks-modal');
            if (e.target === modal) {
                this.closeBookmarksModal();
            }
        });

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            // Ctrl+D لإضافة إشارة مرجعية
            if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
                e.preventDefault();
                this.togglePageBookmark();
            }
            
            // Ctrl+Shift+B لفتح الإشارات المرجعية
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'B') {
                e.preventDefault();
                this.toggleBookmarksModal();
            }
        });
    }

    togglePageBookmark() {
        const existingBookmark = this.findBookmark(this.currentPage.url);
        
        if (existingBookmark) {
            this.deleteBookmark(existingBookmark.id);
            this.showNotification('تم حذف الإشارة المرجعية', 'info');
        } else {
            this.addBookmark({
                type: 'page',
                title: this.currentPage.title,
                url: this.currentPage.url,
                section: null,
                category: this.getPageCategory()
            });
            this.showNotification('تم حفظ الإشارة المرجعية', 'success');
        }
        
        this.updateBookmarkStatus();
    }

    toggleSectionBookmark(sectionId, sectionTitle) {
        const sectionUrl = `${this.currentPage.url}#${sectionId}`;
        const existingBookmark = this.findBookmark(sectionUrl);
        
        if (existingBookmark) {
            this.deleteBookmark(existingBookmark.id);
            this.showNotification('تم حذف الإشارة المرجعية للقسم', 'info');
        } else {
            this.addBookmark({
                type: 'section',
                title: `${this.currentPage.title} - ${sectionTitle}`,
                url: sectionUrl,
                section: sectionTitle,
                category: this.getPageCategory()
            });
            this.showNotification('تم حفظ الإشارة المرجعية للقسم', 'success');
        }
        
        this.updateBookmarkStatus();
    }

    addBookmark(bookmarkData) {
        const bookmark = {
            id: this.generateId(),
            ...bookmarkData,
            dateAdded: new Date().toISOString(),
            pageInfo: this.currentPage
        };
        
        this.bookmarks.unshift(bookmark);
        this.saveBookmarks();
        this.updateBookmarksInterface();
        
        return bookmark.id;
    }

    deleteBookmark(bookmarkId) {
        this.bookmarks = this.bookmarks.filter(bookmark => bookmark.id !== bookmarkId);
        this.saveBookmarks();
        this.updateBookmarksInterface();
    }

    findBookmark(url) {
        return this.bookmarks.find(bookmark => bookmark.url === url);
    }

    getPageCategory() {
        if (this.currentPage.partId) {
            const partNames = {
                part1: 'الأسس والمبادئ',
                part2: 'التطبيقات العملية',
                part3: 'المواضيع المتقدمة'
            };
            return partNames[this.currentPage.partId] || 'عام';
        }
        return 'عام';
    }

    renderBookmarksList(filteredBookmarks = null) {
        const bookmarksToRender = filteredBookmarks || this.bookmarks;
        
        if (bookmarksToRender.length === 0) {
            return `
                <div class="no-bookmarks">
                    <i class="fas fa-bookmark"></i>
                    <h4>لا توجد إشارات مرجعية</h4>
                    <p>ابدأ بحفظ الصفحات والأقسام المهمة لك</p>
                    <div class="bookmark-tips">
                        <h5>نصائح:</h5>
                        <ul>
                            <li>استخدم <kbd>Ctrl+D</kbd> لحفظ الصفحة الحالية</li>
                            <li>انقر على <i class="fas fa-bookmark"></i> بجانب أي قسم لحفظه</li>
                            <li>استخدم <kbd>Ctrl+Shift+B</kbd> لفتح الإشارات المرجعية</li>
                        </ul>
                    </div>
                </div>
            `;
        }
        
        // تجميع الإشارات المرجعية حسب الفئة
        const groupedBookmarks = this.groupBookmarksByCategory(bookmarksToRender);
        
        let html = '';
        Object.entries(groupedBookmarks).forEach(([category, bookmarks]) => {
            html += `
                <div class="bookmark-category">
                    <h4 class="category-title">
                        <i class="fas fa-folder"></i>
                        ${category}
                        <span class="category-count">(${bookmarks.length})</span>
                    </h4>
                    <div class="category-bookmarks">
                        ${bookmarks.map(bookmark => this.renderBookmarkItem(bookmark)).join('')}
                    </div>
                </div>
            `;
        });
        
        return html;
    }

    renderBookmarkItem(bookmark) {
        const dateAdded = new Date(bookmark.dateAdded).toLocaleDateString('ar-SA');
        const timeAgo = this.getTimeAgo(bookmark.dateAdded);
        
        return `
            <div class="bookmark-item" data-bookmark-id="${bookmark.id}">
                <div class="bookmark-icon">
                    <i class="fas ${bookmark.type === 'section' ? 'fa-link' : 'fa-file-alt'}"></i>
                </div>
                
                <div class="bookmark-content">
                    <h5 class="bookmark-title">
                        <a href="${bookmark.url}" target="_blank">${bookmark.title}</a>
                    </h5>
                    
                    ${bookmark.section ? `<p class="bookmark-section">القسم: ${bookmark.section}</p>` : ''}
                    
                    <div class="bookmark-meta">
                        <span class="bookmark-date" title="${dateAdded}">${timeAgo}</span>
                        <span class="bookmark-type">${bookmark.type === 'section' ? 'قسم' : 'صفحة'}</span>
                    </div>
                </div>
                
                <div class="bookmark-actions">
                    <button class="bookmark-action-btn" title="فتح في نافذة جديدة" onclick="window.open('${bookmark.url}', '_blank')">
                        <i class="fas fa-external-link-alt"></i>
                    </button>
                    <button class="bookmark-action-btn copy-bookmark-btn" title="نسخ الرابط" data-url="${bookmark.url}">
                        <i class="fas fa-copy"></i>
                    </button>
                    <button class="bookmark-action-btn delete-bookmark-btn" title="حذف الإشارة المرجعية">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    }

    groupBookmarksByCategory(bookmarks) {
        const grouped = {};
        
        bookmarks.forEach(bookmark => {
            const category = bookmark.category || 'عام';
            if (!grouped[category]) {
                grouped[category] = [];
            }
            grouped[category].push(bookmark);
        });
        
        // ترتيب الفئات
        const sortedGrouped = {};
        const sortedCategories = Object.keys(grouped).sort();
        sortedCategories.forEach(category => {
            sortedGrouped[category] = grouped[category].sort((a, b) => 
                new Date(b.dateAdded) - new Date(a.dateAdded)
            );
        });
        
        return sortedGrouped;
    }

    filterBookmarks(query) {
        if (!query.trim()) {
            this.updateBookmarksList();
            return;
        }
        
        const filteredBookmarks = this.bookmarks.filter(bookmark => 
            bookmark.title.toLowerCase().includes(query.toLowerCase()) ||
            (bookmark.section && bookmark.section.toLowerCase().includes(query.toLowerCase())) ||
            bookmark.category.toLowerCase().includes(query.toLowerCase())
        );
        
        this.updateBookmarksList(filteredBookmarks);
    }

    updateBookmarksList(filteredBookmarks = null) {
        const bookmarksList = document.getElementById('bookmarks-list');
        if (bookmarksList) {
            bookmarksList.innerHTML = this.renderBookmarksList(filteredBookmarks);
        }
    }

    updateBookmarksInterface() {
        // تحديث عداد الإشارات المرجعية
        const countElement = document.querySelector('.bookmarks-count .count-number');
        if (countElement) {
            countElement.textContent = this.bookmarks.length;
        }
        
        // تحديث الإحصائيات
        const statsElements = document.querySelectorAll('.bookmarks-stats .stat-number');
        if (statsElements.length >= 3) {
            statsElements[0].textContent = this.bookmarks.length;
            statsElements[1].textContent = this.getBookmarksThisWeek();
            statsElements[2].textContent = this.getUniqueCategories().length;
        }
        
        // تحديث قائمة الإشارات المرجعية
        this.updateBookmarksList();
    }

    updateBookmarkStatus() {
        // تحديث حالة أزرار الإشارات المرجعية
        const pageBookmarkBtn = document.querySelector('.page-bookmark-btn');
        if (pageBookmarkBtn) {
            const isBookmarked = this.findBookmark(this.currentPage.url);
            pageBookmarkBtn.classList.toggle('bookmarked', !!isBookmarked);
            pageBookmarkBtn.title = isBookmarked ? 'حذف الإشارة المرجعية' : 'إضافة إشارة مرجعية';
        }
        
        // تحديث أزرار الأقسام
        document.querySelectorAll('.section-bookmark-btn').forEach(btn => {
            const sectionId = btn.dataset.sectionId;
            const sectionUrl = `${this.currentPage.url}#${sectionId}`;
            const isBookmarked = this.findBookmark(sectionUrl);
            btn.classList.toggle('bookmarked', !!isBookmarked);
            btn.title = isBookmarked ? 'حذف الإشارة المرجعية' : 'إضافة إشارة مرجعية';
        });
    }

    toggleBookmarksModal() {
        const modal = document.querySelector('.bookmarks-modal');
        if (modal.style.display === 'flex') {
            this.closeBookmarksModal();
        } else {
            this.openBookmarksModal();
        }
    }

    openBookmarksModal() {
        const modal = document.querySelector('.bookmarks-modal');
        modal.style.display = 'flex';
        setTimeout(() => modal.classList.add('active'), 10);
        
        // تحديث المحتوى
        this.updateBookmarksInterface();
        
        // التركيز على حقل البحث
        const searchInput = document.getElementById('bookmarks-search');
        if (searchInput) {
            searchInput.focus();
        }
    }

    closeBookmarksModal() {
        const modal = document.querySelector('.bookmarks-modal');
        modal.classList.remove('active');
        setTimeout(() => modal.style.display = 'none', 300);
    }

    exportBookmarks() {
        const exportData = {
            bookmarks: this.bookmarks,
            exportDate: new Date().toISOString(),
            version: '1.0'
        };
        
        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `aami_bookmarks_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showNotification('تم تصدير الإشارات المرجعية بنجاح', 'success');
    }

    importBookmarks() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const data = JSON.parse(e.target.result);
                    if (data.bookmarks && Array.isArray(data.bookmarks)) {
                        // دمج الإشارات المرجعية المستوردة مع الموجودة
                        const importedBookmarks = data.bookmarks.filter(bookmark => 
                            !this.findBookmark(bookmark.url)
                        );
                        
                        this.bookmarks = [...this.bookmarks, ...importedBookmarks];
                        this.saveBookmarks();
                        this.updateBookmarksInterface();
                        
                        this.showNotification(`تم استيراد ${importedBookmarks.length} إشارة مرجعية`, 'success');
                    } else {
                        this.showNotification('ملف غير صالح', 'error');
                    }
                } catch (error) {
                    this.showNotification('خطأ في قراءة الملف', 'error');
                }
            };
            reader.readAsText(file);
        };
        
        input.click();
    }

    clearAllBookmarks() {
        if (confirm('هل أنت متأكد من حذف جميع الإشارات المرجعية؟')) {
            this.bookmarks = [];
            this.saveBookmarks();
            this.updateBookmarksInterface();
            this.updateBookmarkStatus();
            this.showNotification('تم حذف جميع الإشارات المرجعية', 'info');
        }
    }

    getBookmarksThisWeek() {
        const oneWeekAgo = new Date();
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
        
        return this.bookmarks.filter(bookmark => 
            new Date(bookmark.dateAdded) > oneWeekAgo
        ).length;
    }

    getUniqueCategories() {
        const categories = new Set(this.bookmarks.map(bookmark => bookmark.category || 'عام'));
        return Array.from(categories);
    }

    getTimeAgo(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);
        
        if (diffInSeconds < 60) return 'الآن';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} دقيقة`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} ساعة`;
        if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)} يوم`;
        
        return date.toLocaleDateString('ar-SA');
    }

    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `bookmark-notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
                <span>${message}</span>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => notification.classList.add('show'), 100);
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    // طرق عامة للاستخدام من الخارج
    addCustomBookmark(title, url, category = 'عام') {
        return this.addBookmark({
            type: 'custom',
            title,
            url,
            section: null,
            category
        });
    }

    getBookmarksByCategory(category) {
        return this.bookmarks.filter(bookmark => bookmark.category === category);
    }

    searchBookmarks(query) {
        return this.bookmarks.filter(bookmark => 
            bookmark.title.toLowerCase().includes(query.toLowerCase()) ||
            (bookmark.section && bookmark.section.toLowerCase().includes(query.toLowerCase()))
        );
    }

    getBookmarksCount() {
        return this.bookmarks.length;
    }

    isPageBookmarked(url = null) {
        const targetUrl = url || this.currentPage.url;
        return !!this.findBookmark(targetUrl);
    }
}

// تهيئة نظام الإشارات المرجعية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.bookmarksSystem = new BookmarksSystem();
});
