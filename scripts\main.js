// Main JavaScript functionality for AAMI Standards Guide

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    initNavigation();
    initScrollEffects();
    initAnimations();
    initSearchFunctionality();
    initThemeToggle();
    initAccessibility();
});

// Navigation functionality
function initNavigation() {
    const navToggle = document.getElementById('nav-toggle');
    const navMenu = document.getElementById('nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');

    // Mobile menu toggle
    if (navToggle && navMenu) {
        navToggle.addEventListener('click', function() {
            navToggle.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
    }

    // Smooth scrolling for anchor links
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            
            // Check if it's an anchor link (starts with #)
            if (href && href.startsWith('#')) {
                e.preventDefault();
                const targetId = href.substring(1);
                const targetElement = document.getElementById(targetId);
                
                if (targetElement) {
                    const headerHeight = document.querySelector('.main-header').offsetHeight;
                    const targetPosition = targetElement.offsetTop - headerHeight - 20;
                    
                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                    
                    // Close mobile menu if open
                    if (navMenu.classList.contains('active')) {
                        navToggle.classList.remove('active');
                        navMenu.classList.remove('active');
                    }
                    
                    // Update active link
                    updateActiveNavLink(this);
                }
            }
        });
    });

    // Update active navigation link based on scroll position
    window.addEventListener('scroll', updateActiveNavOnScroll);
}

function updateActiveNavLink(activeLink) {
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    activeLink.classList.add('active');
}

function updateActiveNavOnScroll() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link[href^="#"]');
    const headerHeight = document.querySelector('.main-header').offsetHeight;
    
    let currentSection = '';
    
    sections.forEach(section => {
        const sectionTop = section.offsetTop - headerHeight - 100;
        const sectionHeight = section.offsetHeight;
        
        if (window.scrollY >= sectionTop && window.scrollY < sectionTop + sectionHeight) {
            currentSection = section.getAttribute('id');
        }
    });
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${currentSection}`) {
            link.classList.add('active');
        }
    });
}

// Scroll effects
function initScrollEffects() {
    const header = document.querySelector('.main-header');
    
    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            header.style.background = 'rgba(255, 255, 255, 0.98)';
            header.style.boxShadow = '0 4px 20px rgba(0,0,0,0.1)';
        } else {
            header.style.background = 'rgba(255, 255, 255, 0.95)';
            header.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
        }
    });
}

// Animation on scroll
function initAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animatedElements = document.querySelectorAll('.about-card, .part-card, .quick-link');
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
}

// Search functionality
function initSearchFunctionality() {
    // Create search overlay
    createSearchOverlay();
    
    // Add keyboard shortcut for search (Ctrl+K or Cmd+K)
    document.addEventListener('keydown', function(e) {
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            toggleSearchOverlay();
        }
        
        // Close search with Escape
        if (e.key === 'Escape') {
            closeSearchOverlay();
        }
    });
}

function createSearchOverlay() {
    const searchOverlay = document.createElement('div');
    searchOverlay.id = 'search-overlay';
    searchOverlay.className = 'search-overlay';
    searchOverlay.innerHTML = `
        <div class="search-container">
            <div class="search-header">
                <input type="text" id="search-input" placeholder="ابحث في الدليل..." autocomplete="off">
                <button id="search-close" class="search-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="search-results" id="search-results">
                <div class="search-placeholder">
                    <i class="fas fa-search"></i>
                    <p>ابدأ بكتابة كلمة للبحث في محتويات الدليل</p>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(searchOverlay);
    
    // Add event listeners
    document.getElementById('search-close').addEventListener('click', closeSearchOverlay);
    document.getElementById('search-input').addEventListener('input', handleSearch);
    
    // Close overlay when clicking outside
    searchOverlay.addEventListener('click', function(e) {
        if (e.target === searchOverlay) {
            closeSearchOverlay();
        }
    });
}

function toggleSearchOverlay() {
    const overlay = document.getElementById('search-overlay');
    if (overlay.style.display === 'flex') {
        closeSearchOverlay();
    } else {
        openSearchOverlay();
    }
}

function openSearchOverlay() {
    const overlay = document.getElementById('search-overlay');
    const input = document.getElementById('search-input');
    
    overlay.style.display = 'flex';
    setTimeout(() => {
        overlay.classList.add('active');
        input.focus();
    }, 10);
}

function closeSearchOverlay() {
    const overlay = document.getElementById('search-overlay');
    const input = document.getElementById('search-input');
    
    overlay.classList.remove('active');
    setTimeout(() => {
        overlay.style.display = 'none';
        input.value = '';
        document.getElementById('search-results').innerHTML = `
            <div class="search-placeholder">
                <i class="fas fa-search"></i>
                <p>ابدأ بكتابة كلمة للبحث في محتويات الدليل</p>
            </div>
        `;
    }, 300);
}

function handleSearch(e) {
    const query = e.target.value.trim();
    const resultsContainer = document.getElementById('search-results');
    
    if (query.length < 2) {
        resultsContainer.innerHTML = `
            <div class="search-placeholder">
                <i class="fas fa-search"></i>
                <p>ابدأ بكتابة كلمة للبحث في محتويات الدليل</p>
            </div>
        `;
        return;
    }
    
    // Simulate search results (in a real implementation, this would query actual content)
    const searchResults = performSearch(query);
    displaySearchResults(searchResults);
}

function performSearch(query) {
    // Mock search data - in a real implementation, this would search through actual content
    const mockData = [
        { title: 'مقدمة في عالم المعايير الطبية', url: 'pages/part1/chapter1.html', excerpt: 'تعريف المعايير وأهميتها في الرعاية الصحية' },
        { title: 'فلسفة المعايير: الركائز الثلاث الحاكمة', url: 'pages/part1/chapter2.html', excerpt: 'السلامة والفعالية والوصول' },
        { title: 'بنية المصداقية: النظام البيئي للمعايير', url: 'pages/part1/chapter3.html', excerpt: 'شبكة الثقة العالمية للمعايير' },
        { title: 'خريطة عالم المعايير', url: 'pages/part2/chapter4.html', excerpt: 'إطار عمل من أربع طبقات' },
        { title: 'التطبيق العملي', url: 'pages/part2/chapter5.html', excerpt: 'دور المهندس السريري في دورة حياة التكنولوجيا' },
        { title: 'الذكاء الاصطناعي والأمن السيبراني', url: 'pages/part3/chapter6.html', excerpt: 'جبهات المستقبل في المعايير' },
        { title: 'صندوق أدوات المهندس السريري', url: 'pages/part3/chapter7.html', excerpt: 'الكفاءات الأساسية لعصر المعايير' }
    ];
    
    return mockData.filter(item => 
        item.title.includes(query) || 
        item.excerpt.includes(query)
    );
}

function displaySearchResults(results) {
    const resultsContainer = document.getElementById('search-results');
    
    if (results.length === 0) {
        resultsContainer.innerHTML = `
            <div class="search-no-results">
                <i class="fas fa-search"></i>
                <p>لم يتم العثور على نتائج</p>
            </div>
        `;
        return;
    }
    
    const resultsHTML = results.map(result => `
        <div class="search-result-item">
            <h4><a href="${result.url}">${result.title}</a></h4>
            <p>${result.excerpt}</p>
        </div>
    `).join('');
    
    resultsContainer.innerHTML = resultsHTML;
}

// Theme toggle functionality
function initThemeToggle() {
    // This can be expanded to include dark/light theme switching
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)');
    
    // Listen for changes in system theme preference
    prefersDark.addEventListener('change', function(e) {
        // Handle theme change if needed
        console.log('System theme changed to:', e.matches ? 'dark' : 'light');
    });
}

// Accessibility enhancements
function initAccessibility() {
    // Skip to main content link
    const skipLink = document.createElement('a');
    skipLink.href = '#main-content';
    skipLink.textContent = 'تخطي إلى المحتوى الرئيسي';
    skipLink.className = 'skip-link';
    skipLink.style.cssText = `
        position: absolute;
        top: -40px;
        left: 6px;
        background: var(--primary-color);
        color: white;
        padding: 8px;
        text-decoration: none;
        border-radius: 4px;
        z-index: 1001;
        transition: top 0.3s;
    `;
    
    skipLink.addEventListener('focus', function() {
        this.style.top = '6px';
    });
    
    skipLink.addEventListener('blur', function() {
        this.style.top = '-40px';
    });
    
    document.body.insertBefore(skipLink, document.body.firstChild);
    
    // Add main content landmark
    const heroSection = document.querySelector('.hero');
    if (heroSection) {
        heroSection.setAttribute('id', 'main-content');
        heroSection.setAttribute('role', 'main');
    }
    
    // Enhance keyboard navigation
    document.addEventListener('keydown', function(e) {
        // Add custom keyboard shortcuts here if needed
        if (e.key === 'Tab') {
            document.body.classList.add('keyboard-navigation');
        }
    });
    
    document.addEventListener('mousedown', function() {
        document.body.classList.remove('keyboard-navigation');
    });
}

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Export functions for use in other scripts
window.AAMIGuide = {
    openSearchOverlay,
    closeSearchOverlay,
    updateActiveNavLink
};
