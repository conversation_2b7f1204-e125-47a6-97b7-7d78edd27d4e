// Main JavaScript functionality for AAMI Standards Guide

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    initNavigation();
    initScrollEffects();
    initAnimations();
    initSearchFunctionality();
    initThemeToggle();
    initAccessibility();
});

// Navigation functionality
function initNavigation() {
    const navToggle = document.getElementById('nav-toggle');
    const navMenu = document.getElementById('nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');

    // Mobile menu toggle
    if (navToggle && navMenu) {
        navToggle.addEventListener('click', function() {
            navToggle.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
    }

    // Smooth scrolling for anchor links
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            
            // Check if it's an anchor link (starts with #)
            if (href && href.startsWith('#')) {
                e.preventDefault();
                const targetId = href.substring(1);
                const targetElement = document.getElementById(targetId);
                
                if (targetElement) {
                    const headerHeight = document.querySelector('.main-header').offsetHeight;
                    const targetPosition = targetElement.offsetTop - headerHeight - 20;
                    
                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                    
                    // Close mobile menu if open
                    if (navMenu.classList.contains('active')) {
                        navToggle.classList.remove('active');
                        navMenu.classList.remove('active');
                    }
                    
                    // Update active link
                    updateActiveNavLink(this);
                }
            }
        });
    });

    // Update active navigation link based on scroll position
    window.addEventListener('scroll', updateActiveNavOnScroll);
}

function updateActiveNavLink(activeLink) {
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    activeLink.classList.add('active');
}

function updateActiveNavOnScroll() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link[href^="#"]');
    const headerHeight = document.querySelector('.main-header').offsetHeight;
    
    let currentSection = '';
    
    sections.forEach(section => {
        const sectionTop = section.offsetTop - headerHeight - 100;
        const sectionHeight = section.offsetHeight;
        
        if (window.scrollY >= sectionTop && window.scrollY < sectionTop + sectionHeight) {
            currentSection = section.getAttribute('id');
        }
    });
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${currentSection}`) {
            link.classList.add('active');
        }
    });
}

// Scroll effects
function initScrollEffects() {
    const header = document.querySelector('.main-header');
    
    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            header.style.background = 'rgba(255, 255, 255, 0.98)';
            header.style.boxShadow = '0 4px 20px rgba(0,0,0,0.1)';
        } else {
            header.style.background = 'rgba(255, 255, 255, 0.95)';
            header.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
        }
    });
}

// Animation on scroll
function initAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animatedElements = document.querySelectorAll('.about-card, .part-card, .quick-link');
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
}

// Advanced Search functionality
function initSearchFunctionality() {
    // Create advanced search overlay
    createAdvancedSearchOverlay();

    // Add keyboard shortcut for search (Ctrl+K or Cmd+K)
    document.addEventListener('keydown', function(e) {
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            toggleSearchOverlay();
        }

        // Close search with Escape
        if (e.key === 'Escape') {
            closeSearchOverlay();
        }
    });

    // Initialize search index
    initializeSearchIndex();
}

function createSearchOverlay() {
    const searchOverlay = document.createElement('div');
    searchOverlay.id = 'search-overlay';
    searchOverlay.className = 'search-overlay';
    searchOverlay.innerHTML = `
        <div class="search-container">
            <div class="search-header">
                <input type="text" id="search-input" placeholder="ابحث في الدليل..." autocomplete="off">
                <button id="search-close" class="search-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="search-results" id="search-results">
                <div class="search-placeholder">
                    <i class="fas fa-search"></i>
                    <p>ابدأ بكتابة كلمة للبحث في محتويات الدليل</p>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(searchOverlay);
    
    // Add event listeners
    document.getElementById('search-close').addEventListener('click', closeSearchOverlay);
    document.getElementById('search-input').addEventListener('input', handleSearch);
    
    // Close overlay when clicking outside
    searchOverlay.addEventListener('click', function(e) {
        if (e.target === searchOverlay) {
            closeSearchOverlay();
        }
    });
}

function toggleSearchOverlay() {
    const overlay = document.getElementById('search-overlay');
    if (overlay.style.display === 'flex') {
        closeSearchOverlay();
    } else {
        openSearchOverlay();
    }
}

function openSearchOverlay() {
    const overlay = document.getElementById('search-overlay');
    const input = document.getElementById('search-input');
    
    overlay.style.display = 'flex';
    setTimeout(() => {
        overlay.classList.add('active');
        input.focus();
    }, 10);
}

function closeSearchOverlay() {
    const overlay = document.getElementById('search-overlay');
    const input = document.getElementById('search-input');
    
    overlay.classList.remove('active');
    setTimeout(() => {
        overlay.style.display = 'none';
        input.value = '';
        document.getElementById('search-results').innerHTML = `
            <div class="search-placeholder">
                <i class="fas fa-search"></i>
                <p>ابدأ بكتابة كلمة للبحث في محتويات الدليل</p>
            </div>
        `;
    }, 300);
}

function handleSearch(e) {
    const query = e.target.value.trim();
    const resultsContainer = document.getElementById('search-results');
    
    if (query.length < 2) {
        resultsContainer.innerHTML = `
            <div class="search-placeholder">
                <i class="fas fa-search"></i>
                <p>ابدأ بكتابة كلمة للبحث في محتويات الدليل</p>
            </div>
        `;
        return;
    }
    
    // Simulate search results (in a real implementation, this would query actual content)
    const searchResults = performSearch(query);
    displaySearchResults(searchResults);
}

function performSearch(query) {
    // Mock search data - in a real implementation, this would search through actual content
    const mockData = [
        { title: 'مقدمة في عالم المعايير الطبية', url: 'pages/part1/chapter1.html', excerpt: 'تعريف المعايير وأهميتها في الرعاية الصحية' },
        { title: 'فلسفة المعايير: الركائز الثلاث الحاكمة', url: 'pages/part1/chapter2.html', excerpt: 'السلامة والفعالية والوصول' },
        { title: 'بنية المصداقية: النظام البيئي للمعايير', url: 'pages/part1/chapter3.html', excerpt: 'شبكة الثقة العالمية للمعايير' },
        { title: 'خريطة عالم المعايير', url: 'pages/part2/chapter4.html', excerpt: 'إطار عمل من أربع طبقات' },
        { title: 'التطبيق العملي', url: 'pages/part2/chapter5.html', excerpt: 'دور المهندس السريري في دورة حياة التكنولوجيا' },
        { title: 'الذكاء الاصطناعي والأمن السيبراني', url: 'pages/part3/chapter6.html', excerpt: 'جبهات المستقبل في المعايير' },
        { title: 'صندوق أدوات المهندس السريري', url: 'pages/part3/chapter7.html', excerpt: 'الكفاءات الأساسية لعصر المعايير' }
    ];
    
    return mockData.filter(item => 
        item.title.includes(query) || 
        item.excerpt.includes(query)
    );
}

function displaySearchResults(results) {
    const resultsContainer = document.getElementById('search-results');
    
    if (results.length === 0) {
        resultsContainer.innerHTML = `
            <div class="search-no-results">
                <i class="fas fa-search"></i>
                <p>لم يتم العثور على نتائج</p>
            </div>
        `;
        return;
    }
    
    const resultsHTML = results.map(result => `
        <div class="search-result-item">
            <h4><a href="${result.url}">${result.title}</a></h4>
            <p>${result.excerpt}</p>
        </div>
    `).join('');
    
    resultsContainer.innerHTML = resultsHTML;
}

// Theme toggle functionality
function initThemeToggle() {
    // This can be expanded to include dark/light theme switching
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)');
    
    // Listen for changes in system theme preference
    prefersDark.addEventListener('change', function(e) {
        // Handle theme change if needed
        console.log('System theme changed to:', e.matches ? 'dark' : 'light');
    });
}

// Accessibility enhancements
function initAccessibility() {
    // Skip to main content link
    const skipLink = document.createElement('a');
    skipLink.href = '#main-content';
    skipLink.textContent = 'تخطي إلى المحتوى الرئيسي';
    skipLink.className = 'skip-link';
    skipLink.style.cssText = `
        position: absolute;
        top: -40px;
        left: 6px;
        background: var(--primary-color);
        color: white;
        padding: 8px;
        text-decoration: none;
        border-radius: 4px;
        z-index: 1001;
        transition: top 0.3s;
    `;
    
    skipLink.addEventListener('focus', function() {
        this.style.top = '6px';
    });
    
    skipLink.addEventListener('blur', function() {
        this.style.top = '-40px';
    });
    
    document.body.insertBefore(skipLink, document.body.firstChild);
    
    // Add main content landmark
    const heroSection = document.querySelector('.hero');
    if (heroSection) {
        heroSection.setAttribute('id', 'main-content');
        heroSection.setAttribute('role', 'main');
    }
    
    // Enhance keyboard navigation
    document.addEventListener('keydown', function(e) {
        // Add custom keyboard shortcuts here if needed
        if (e.key === 'Tab') {
            document.body.classList.add('keyboard-navigation');
        }
    });
    
    document.addEventListener('mousedown', function() {
        document.body.classList.remove('keyboard-navigation');
    });
}

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Export functions for use in other scripts
window.AAMIGuide = {
    openSearchOverlay,
    closeSearchOverlay,
    updateActiveNavLink
};

// تهيئة الصفحة عند التحميل
document.addEventListener('DOMContentLoaded', function() {
    // التهيئة الأساسية
    initializeNavigation();
    initSearchFunctionality();
    initAccessibility();

    // تحسينات الأداء
    optimizePerformance();

    // تحسينات تجربة المستخدم
    enhanceUserExperience();

    // إضافة العناصر الإضافية
    addBackToTopButton();

    // تحميل الأنظمة التفاعلية
    loadInteractiveSystems();

    // إضافة تأثيرات التمرير المحسنة
    optimizeScrolling();

    console.log('تم تحميل الصفحة بنجاح مع جميع التحسينات');
});

// تحميل الأنظمة التفاعلية المتقدمة
function loadInteractiveSystems() {
    const scriptsToLoad = [
        // الأنظمة الأساسية - تحميل في جميع الصفحات
        { name: 'progress-tracker.js', condition: () => typeof ProgressTracker === 'undefined' },
        { name: 'advanced-search.js', condition: () => typeof AdvancedSearchEngine === 'undefined' },
        { name: 'bookmarks-system.js', condition: () => typeof BookmarksSystem === 'undefined' },
        { name: 'certificates-system.js', condition: () => typeof CertificatesSystem === 'undefined' },
        { name: 'resources-library.js', condition: () => typeof ResourcesLibrary === 'undefined' },
        { name: 'glossary-system.js', condition: () => typeof GlossarySystem === 'undefined' },

        // الأنظمة المشروطة
        {
            name: 'quiz-system.js',
            condition: () => document.querySelector('.content-section') && typeof QuizSystem === 'undefined'
        },
        {
            name: 'risk-calculator.js',
            condition: () => document.querySelector('#section-5-3') && typeof RiskCalculator === 'undefined'
        },
        {
            name: 'interactive-checklists.js',
            condition: () => (document.querySelector('#section-6-3') || document.querySelector('#section-5-4') || document.querySelector('#section-7-4')) && typeof InteractiveChecklists === 'undefined'
        },
        {
            name: 'case-studies.js',
            condition: () => (document.querySelector('.chapter-content') || document.querySelector('.part-index')) && typeof CaseStudiesSystem === 'undefined'
        }
    ];

    // تحميل الملفات المطلوبة
    scriptsToLoad.forEach(script => {
        if (script.condition()) {
            loadScript(script.name);
        }
    });
}

// تحميل ملف JavaScript
function loadScript(filename) {
    const script = document.createElement('script');
    script.src = getScriptPath(filename);
    script.async = true;

    // إضافة معالج للأخطاء
    script.onerror = () => {
        console.warn(`فشل في تحميل الملف: ${filename}`);
    };

    // إضافة معالج للنجاح
    script.onload = () => {
        console.log(`تم تحميل الملف بنجاح: ${filename}`);
    };

    document.head.appendChild(script);
}

// الحصول على مسار الملف النصي بناءً على الموقع الحالي
function getScriptPath(filename) {
    const currentPath = window.location.pathname;
    const depth = (currentPath.match(/\//g) || []).length - 1;

    let relativePath = '';
    for (let i = 0; i < depth; i++) {
        relativePath += '../';
    }

    return relativePath + 'scripts/' + filename;
}

// تحسينات الأداء
function optimizePerformance() {
    // تحسين الصور
    optimizeImages();

    // تحسين التمرير
    optimizeScrolling();

    // تحسين الذاكرة
    optimizeMemory();

    // تحسين التحميل
    optimizeLoading();
}

function optimizeImages() {
    // تحميل الصور بشكل تدريجي (Lazy Loading)
    const images = document.querySelectorAll('img[data-src]');

    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        images.forEach(img => imageObserver.observe(img));
    } else {
        // Fallback للمتصفحات القديمة
        images.forEach(img => {
            img.src = img.dataset.src;
            img.classList.remove('lazy');
        });
    }
}

function optimizeScrolling() {
    // تحسين أداء التمرير باستخدام throttling
    let ticking = false;

    function updateScrollElements() {
        // تحديث عناصر التمرير
        updateActiveNavLink();

        // إظهار/إخفاء زر العودة للأعلى
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const backToTopBtn = document.querySelector('.back-to-top');

        if (backToTopBtn) {
            if (scrollTop > 300) {
                backToTopBtn.classList.add('visible');
            } else {
                backToTopBtn.classList.remove('visible');
            }
        }

        ticking = false;
    }

    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(updateScrollElements);
            ticking = true;
        }
    }

    window.addEventListener('scroll', requestTick, { passive: true });
}

function optimizeMemory() {
    // تنظيف الذاكرة من العناصر غير المستخدمة
    const cleanupInterval = setInterval(() => {
        // إزالة العناصر المؤقتة القديمة
        const tempElements = document.querySelectorAll('.temp-element');
        tempElements.forEach(element => {
            const created = element.dataset.created;
            if (created && Date.now() - parseInt(created) > 300000) { // 5 دقائق
                element.remove();
            }
        });

        // تنظيف الإشعارات القديمة
        const notifications = document.querySelectorAll('.notification:not(.show)');
        notifications.forEach(notification => {
            if (!notification.classList.contains('show')) {
                notification.remove();
            }
        });
    }, 60000); // كل دقيقة

    // تنظيف عند إغلاق الصفحة
    window.addEventListener('beforeunload', () => {
        clearInterval(cleanupInterval);
    });
}

function optimizeLoading() {
    // ضغط البيانات المحلية
    const originalSetItem = localStorage.setItem;
    localStorage.setItem = function(key, value) {
        try {
            // ضغط البيانات الكبيرة
            if (value.length > 10000) {
                value = LZString.compress(value);
            }
            originalSetItem.call(this, key, value);
        } catch (error) {
            console.warn('خطأ في حفظ البيانات:', error);
        }
    };

    // تحسين تحميل الخطوط
    if ('fonts' in document) {
        document.fonts.ready.then(() => {
            document.body.classList.add('fonts-loaded');
        });
    }

    // تحسين تحميل CSS
    const criticalCSS = document.querySelector('style[data-critical]');
    if (criticalCSS) {
        // تحميل CSS غير الحرج بشكل غير متزامن
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = 'styles/main.css';
        link.media = 'print';
        link.onload = function() {
            this.media = 'all';
        };
        document.head.appendChild(link);
    }
}

// إضافة زر العودة للأعلى
function addBackToTopButton() {
    const backToTopBtn = document.createElement('button');
    backToTopBtn.className = 'back-to-top';
    backToTopBtn.innerHTML = '<i class="fas fa-arrow-up"></i>';
    backToTopBtn.title = 'العودة للأعلى';

    backToTopBtn.addEventListener('click', () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });

    document.body.appendChild(backToTopBtn);
}

// تحسين تجربة المستخدم
function enhanceUserExperience() {
    // إضافة مؤشرات التحميل
    addLoadingIndicators();

    // تحسين التنقل بلوحة المفاتيح
    enhanceKeyboardNavigation();

    // إضافة اختصارات لوحة المفاتيح
    addKeyboardShortcuts();

    // تحسين الاستجابة للمس
    enhanceTouchExperience();
}

function addLoadingIndicators() {
    // إضافة مؤشر تحميل عام
    const loadingIndicator = document.createElement('div');
    loadingIndicator.className = 'global-loading-indicator';
    loadingIndicator.innerHTML = `
        <div class="loading-spinner"></div>
        <span>جاري التحميل...</span>
    `;
    document.body.appendChild(loadingIndicator);

    // إخفاء المؤشر عند اكتمال التحميل
    window.addEventListener('load', () => {
        setTimeout(() => {
            loadingIndicator.classList.add('hidden');
            setTimeout(() => loadingIndicator.remove(), 500);
        }, 500);
    });
}

function enhanceKeyboardNavigation() {
    // تحسين التنقل بـ Tab
    const focusableElements = 'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])';

    document.addEventListener('keydown', (e) => {
        if (e.key === 'Tab') {
            const focusable = Array.from(document.querySelectorAll(focusableElements));
            const currentIndex = focusable.indexOf(document.activeElement);

            if (e.shiftKey) {
                // Shift + Tab - التنقل للخلف
                if (currentIndex === 0) {
                    e.preventDefault();
                    focusable[focusable.length - 1].focus();
                }
            } else {
                // Tab - التنقل للأمام
                if (currentIndex === focusable.length - 1) {
                    e.preventDefault();
                    focusable[0].focus();
                }
            }
        }
    });
}

function addKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
        // تجاهل الاختصارات إذا كان المستخدم يكتب في حقل نص
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
            return;
        }

        // اختصارات لوحة المفاتيح
        if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case 'k': // البحث
                    e.preventDefault();
                    if (window.openSearchOverlay) {
                        window.openSearchOverlay();
                    }
                    break;
                case 'd': // إضافة إشارة مرجعية
                    e.preventDefault();
                    if (window.bookmarksSystem) {
                        window.bookmarksSystem.togglePageBookmark();
                    }
                    break;
                case 'h': // الصفحة الرئيسية
                    e.preventDefault();
                    window.location.href = '/index.html';
                    break;
            }

            if (e.shiftKey) {
                switch (e.key) {
                    case 'B': // الإشارات المرجعية
                        e.preventDefault();
                        if (window.bookmarksSystem) {
                            window.bookmarksSystem.toggleBookmarksModal();
                        }
                        break;
                    case 'P': // تتبع التقدم
                        e.preventDefault();
                        if (window.progressTracker) {
                            window.progressTracker.toggleDashboard();
                        }
                        break;
                }
            }
        }

        // اختصارات بدون Ctrl
        switch (e.key) {
            case 'Escape': // إغلاق النوافذ المنبثقة
                closeAllModals();
                break;
            case '?': // عرض مساعدة الاختصارات
                e.preventDefault();
                showKeyboardShortcutsHelp();
                break;
        }
    });
}

function enhanceTouchExperience() {
    // تحسين التفاعل باللمس للأجهزة المحمولة
    if ('ontouchstart' in window) {
        document.body.classList.add('touch-device');

        // إضافة دعم السحب للتنقل
        let startX, startY, currentX, currentY;

        document.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        }, { passive: true });

        document.addEventListener('touchmove', (e) => {
            if (!startX || !startY) return;

            currentX = e.touches[0].clientX;
            currentY = e.touches[0].clientY;

            const diffX = startX - currentX;
            const diffY = startY - currentY;

            // السحب الأفقي للتنقل بين الصفحات
            if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
                if (diffX > 0) {
                    // السحب لليسار - الصفحة التالية
                    navigateToNextPage();
                } else {
                    // السحب لليمين - الصفحة السابقة
                    navigateToPreviousPage();
                }

                startX = null;
                startY = null;
            }
        }, { passive: true });
    }
}

function closeAllModals() {
    // إغلاق جميع النوافذ المنبثقة
    const modals = document.querySelectorAll('.modal, .overlay, .popup');
    modals.forEach(modal => {
        if (modal.style.display === 'flex' || modal.classList.contains('active')) {
            modal.classList.remove('active');
            modal.style.display = 'none';
        }
    });
}

function showKeyboardShortcutsHelp() {
    const helpModal = document.createElement('div');
    helpModal.className = 'keyboard-shortcuts-modal';
    helpModal.innerHTML = `
        <div class="shortcuts-modal-content">
            <div class="shortcuts-header">
                <h3>اختصارات لوحة المفاتيح</h3>
                <button class="shortcuts-close-btn">×</button>
            </div>
            <div class="shortcuts-list">
                <div class="shortcut-item">
                    <kbd>Ctrl</kbd> + <kbd>K</kbd>
                    <span>فتح البحث</span>
                </div>
                <div class="shortcut-item">
                    <kbd>Ctrl</kbd> + <kbd>D</kbd>
                    <span>إضافة إشارة مرجعية</span>
                </div>
                <div class="shortcut-item">
                    <kbd>Ctrl</kbd> + <kbd>Shift</kbd> + <kbd>B</kbd>
                    <span>فتح الإشارات المرجعية</span>
                </div>
                <div class="shortcut-item">
                    <kbd>Ctrl</kbd> + <kbd>Shift</kbd> + <kbd>P</kbd>
                    <span>فتح تتبع التقدم</span>
                </div>
                <div class="shortcut-item">
                    <kbd>Ctrl</kbd> + <kbd>H</kbd>
                    <span>الصفحة الرئيسية</span>
                </div>
                <div class="shortcut-item">
                    <kbd>Esc</kbd>
                    <span>إغلاق النوافذ المنبثقة</span>
                </div>
                <div class="shortcut-item">
                    <kbd>?</kbd>
                    <span>عرض هذه المساعدة</span>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(helpModal);

    helpModal.querySelector('.shortcuts-close-btn').addEventListener('click', () => {
        helpModal.remove();
    });

    helpModal.addEventListener('click', (e) => {
        if (e.target === helpModal) {
            helpModal.remove();
        }
    });

    setTimeout(() => helpModal.classList.add('active'), 10);
}

function navigateToNextPage() {
    // منطق التنقل للصفحة التالية
    const nextLink = document.querySelector('.next-page-link, .pagination .next');
    if (nextLink) {
        nextLink.click();
    }
}

function navigateToPreviousPage() {
    // منطق التنقل للصفحة السابقة
    const prevLink = document.querySelector('.prev-page-link, .pagination .prev');
    if (prevLink) {
        prevLink.click();
    }
}
