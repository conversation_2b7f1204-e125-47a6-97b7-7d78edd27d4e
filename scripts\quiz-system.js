/**
 * نظام الاختبارات التفاعلية للدليل العملي لمعايير AAMI
 * Interactive Quiz System for AAMI Standards Practical Guide
 */

class QuizSystem {
    constructor() {
        this.currentQuiz = null;
        this.currentQuestionIndex = 0;
        this.userAnswers = [];
        this.score = 0;
        this.quizData = {};
        this.init();
    }

    init() {
        this.loadQuizData();
        this.setupEventListeners();
    }

    loadQuizData() {
        // بيانات الاختبارات لكل فصل
        this.quizData = {
            'chapter1': {
                title: 'اختبار الفصل الأول: مقدمة في عالم المعايير الطبية',
                questions: [
                    {
                        question: 'ما هو الهدف الأساسي من معايير AAMI؟',
                        options: [
                            'تقليل تكلفة الأجهزة الطبية',
                            'ضمان سلامة وفعالية الأجهزة الطبية',
                            'زيادة سرعة تطوير الأجهزة',
                            'تسهيل عملية البيع والشراء'
                        ],
                        correct: 1,
                        explanation: 'الهدف الأساسي من معايير AAMI هو ضمان سلامة وفعالية الأجهزة الطبية لحماية المرضى والمشغلين.'
                    },
                    {
                        question: 'أي من التالي يُعتبر من المعايير الأساسية لـ AAMI؟',
                        options: [
                            'AAMI ST79 للتعقيم بالبخار',
                            'ISO 9001 لإدارة الجودة',
                            'IEEE 802.11 للشبكات اللاسلكية',
                            'HTML5 لتطوير المواقع'
                        ],
                        correct: 0,
                        explanation: 'AAMI ST79 هو معيار AAMI للتعقيم بالبخار، بينما الخيارات الأخرى تنتمي لمنظمات مختلفة.'
                    },
                    {
                        question: 'ما هي العلاقة بين AAMI و FDA؟',
                        options: [
                            'AAMI تحل محل FDA في الموافقات',
                            'FDA تعتمد على معايير AAMI في تقييم الأجهزة',
                            'لا توجد علاقة بينهما',
                            'AAMI جزء من FDA'
                        ],
                        correct: 1,
                        explanation: 'إدارة الغذاء والدواء الأمريكية (FDA) تعتمد على معايير AAMI كمرجع في تقييم سلامة وفعالية الأجهزة الطبية.'
                    }
                ]
            },
            'chapter2': {
                title: 'اختبار الفصل الثاني: فلسفة المعايير',
                questions: [
                    {
                        question: 'ما هي الركائز الثلاث الحاكمة للمعايير الطبية؟',
                        options: [
                            'السلامة، الفعالية، الجودة',
                            'التكلفة، السرعة، الدقة',
                            'التصميم، التصنيع، التسويق',
                            'البحث، التطوير، التطبيق'
                        ],
                        correct: 0,
                        explanation: 'الركائز الثلاث الحاكمة هي السلامة (Safety)، الفعالية (Efficacy)، والجودة (Quality).'
                    },
                    {
                        question: 'أي من التالي يُعتبر مثالاً على تطبيق مبدأ السلامة؟',
                        options: [
                            'تقليل وزن الجهاز',
                            'زيادة سرعة المعالجة',
                            'اختبار التسرب الكهربائي',
                            'تحسين واجهة المستخدم'
                        ],
                        correct: 2,
                        explanation: 'اختبار التسرب الكهربائي هو إجراء أمان أساسي لحماية المرضى والمشغلين من الصدمات الكهربائية.'
                    }
                ]
            },
            'chapter4': {
                title: 'اختبار الفصل الرابع: دورة حياة الأجهزة الطبية',
                questions: [
                    {
                        question: 'ما هي المرحلة الأولى في دورة حياة الجهاز الطبي؟',
                        options: [
                            'الشراء والاقتناء',
                            'التخطيط وتحديد الاحتياجات',
                            'التركيب والتكليف',
                            'التشغيل والصيانة'
                        ],
                        correct: 1,
                        explanation: 'التخطيط وتحديد الاحتياجات هي المرحلة الأولى والأهم، حيث تؤثر القرارات المتخذة على جميع المراحل اللاحقة.'
                    },
                    {
                        question: 'أي من التالي يُعتبر من اختبارات القبول الأساسية؟',
                        options: [
                            'اختبار السرعة فقط',
                            'اختبار اللون والشكل',
                            'اختبار السلامة الكهربائية والأداء الوظيفي',
                            'اختبار التعبئة والتغليف'
                        ],
                        correct: 2,
                        explanation: 'اختبارات القبول يجب أن تشمل السلامة الكهربائية والأداء الوظيفي لضمان عمل الجهاز بأمان وفعالية.'
                    }
                ]
            },
            'chapter5': {
                title: 'اختبار الفصل الخامس: إدارة المخاطر',
                questions: [
                    {
                        question: 'ما هو المعيار الدولي لإدارة المخاطر في الأجهزة الطبية؟',
                        options: [
                            'ISO 9001',
                            'ISO 14971',
                            'ISO 27001',
                            'ISO 45001'
                        ],
                        correct: 1,
                        explanation: 'ISO 14971 هو المعيار الدولي المخصص لإدارة المخاطر في الأجهزة الطبية.'
                    },
                    {
                        question: 'كيف يتم حساب مستوى المخاطر؟',
                        options: [
                            'الاحتمالية + الشدة',
                            'الاحتمالية × الشدة',
                            'الاحتمالية ÷ الشدة',
                            'الاحتمالية - الشدة'
                        ],
                        correct: 1,
                        explanation: 'مستوى المخاطر = الاحتمالية × الشدة، وهذا يعطي تقييماً شاملاً للمخاطر.'
                    }
                ]
            }
        };
    }

    setupEventListeners() {
        document.addEventListener('DOMContentLoaded', () => {
            this.createQuizButtons();
        });
    }

    createQuizButtons() {
        // البحث عن الفصول وإضافة أزرار الاختبار
        const chapters = document.querySelectorAll('.content-section');
        chapters.forEach(chapter => {
            const chapterId = chapter.id;
            if (this.quizData[chapterId]) {
                this.addQuizButton(chapter, chapterId);
            }
        });
    }

    addQuizButton(chapter, chapterId) {
        const quizButton = document.createElement('div');
        quizButton.className = 'quiz-section';
        quizButton.innerHTML = `
            <div class="quiz-intro">
                <h4>
                    <i class="fas fa-question-circle"></i>
                    اختبر معرفتك
                </h4>
                <p>اختبار قصير لتقييم فهمك لمحتوى هذا الفصل</p>
                <button class="btn btn-primary quiz-start-btn" data-chapter="${chapterId}">
                    <i class="fas fa-play"></i>
                    ابدأ الاختبار
                </button>
            </div>
        `;
        
        chapter.appendChild(quizButton);
        
        // إضافة مستمع الحدث
        const startBtn = quizButton.querySelector('.quiz-start-btn');
        startBtn.addEventListener('click', () => this.startQuiz(chapterId));
    }

    startQuiz(chapterId) {
        this.currentQuiz = this.quizData[chapterId];
        this.currentQuestionIndex = 0;
        this.userAnswers = [];
        this.score = 0;
        
        this.createQuizModal();
        this.showQuestion();
    }

    createQuizModal() {
        // إزالة أي modal موجود
        const existingModal = document.querySelector('.quiz-modal');
        if (existingModal) {
            existingModal.remove();
        }

        const modal = document.createElement('div');
        modal.className = 'quiz-modal';
        modal.innerHTML = `
            <div class="quiz-modal-content">
                <div class="quiz-header">
                    <h3 class="quiz-title">${this.currentQuiz.title}</h3>
                    <button class="quiz-close-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="quiz-progress">
                    <div class="quiz-progress-bar">
                        <div class="quiz-progress-fill"></div>
                    </div>
                    <span class="quiz-progress-text">السؤال 1 من ${this.currentQuiz.questions.length}</span>
                </div>
                <div class="quiz-content">
                    <!-- سيتم ملء المحتوى هنا -->
                </div>
                <div class="quiz-navigation">
                    <button class="btn btn-secondary quiz-prev-btn" disabled>
                        <i class="fas fa-arrow-right"></i>
                        السابق
                    </button>
                    <button class="btn btn-primary quiz-next-btn">
                        التالي
                        <i class="fas fa-arrow-left"></i>
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // إضافة مستمعي الأحداث
        modal.querySelector('.quiz-close-btn').addEventListener('click', () => this.closeQuiz());
        modal.querySelector('.quiz-prev-btn').addEventListener('click', () => this.previousQuestion());
        modal.querySelector('.quiz-next-btn').addEventListener('click', () => this.nextQuestion());
        
        // إغلاق عند النقر خارج المحتوى
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeQuiz();
            }
        });
    }

    showQuestion() {
        const question = this.currentQuiz.questions[this.currentQuestionIndex];
        const quizContent = document.querySelector('.quiz-content');
        
        quizContent.innerHTML = `
            <div class="quiz-question">
                <h4>${question.question}</h4>
                <div class="quiz-options">
                    ${question.options.map((option, index) => `
                        <label class="quiz-option">
                            <input type="radio" name="quiz-answer" value="${index}">
                            <span class="quiz-option-text">${option}</span>
                        </label>
                    `).join('')}
                </div>
            </div>
        `;

        // تحديث شريط التقدم
        const progress = ((this.currentQuestionIndex + 1) / this.currentQuiz.questions.length) * 100;
        document.querySelector('.quiz-progress-fill').style.width = `${progress}%`;
        document.querySelector('.quiz-progress-text').textContent = 
            `السؤال ${this.currentQuestionIndex + 1} من ${this.currentQuiz.questions.length}`;

        // تحديث أزرار التنقل
        const prevBtn = document.querySelector('.quiz-prev-btn');
        const nextBtn = document.querySelector('.quiz-next-btn');
        
        prevBtn.disabled = this.currentQuestionIndex === 0;
        
        if (this.currentQuestionIndex === this.currentQuiz.questions.length - 1) {
            nextBtn.innerHTML = '<i class="fas fa-check"></i> إنهاء الاختبار';
        } else {
            nextBtn.innerHTML = 'التالي <i class="fas fa-arrow-left"></i>';
        }

        // استعادة الإجابة المحفوظة إن وجدت
        if (this.userAnswers[this.currentQuestionIndex] !== undefined) {
            const savedAnswer = this.userAnswers[this.currentQuestionIndex];
            const radioBtn = quizContent.querySelector(`input[value="${savedAnswer}"]`);
            if (radioBtn) {
                radioBtn.checked = true;
            }
        }
    }

    nextQuestion() {
        // حفظ الإجابة الحالية
        const selectedAnswer = document.querySelector('input[name="quiz-answer"]:checked');
        if (selectedAnswer) {
            this.userAnswers[this.currentQuestionIndex] = parseInt(selectedAnswer.value);
        }

        if (this.currentQuestionIndex < this.currentQuiz.questions.length - 1) {
            this.currentQuestionIndex++;
            this.showQuestion();
        } else {
            this.finishQuiz();
        }
    }

    previousQuestion() {
        if (this.currentQuestionIndex > 0) {
            this.currentQuestionIndex--;
            this.showQuestion();
        }
    }

    finishQuiz() {
        // حساب النتيجة
        this.score = 0;
        this.currentQuiz.questions.forEach((question, index) => {
            if (this.userAnswers[index] === question.correct) {
                this.score++;
            }
        });

        this.showResults();
    }

    showResults() {
        const percentage = Math.round((this.score / this.currentQuiz.questions.length) * 100);
        const quizContent = document.querySelector('.quiz-content');
        
        let resultClass = 'excellent';
        let resultMessage = 'ممتاز! لديك فهم عميق للمحتوى.';
        let resultIcon = 'fas fa-trophy';
        
        if (percentage < 60) {
            resultClass = 'needs-improvement';
            resultMessage = 'يُنصح بمراجعة المحتوى مرة أخرى.';
            resultIcon = 'fas fa-book-open';
        } else if (percentage < 80) {
            resultClass = 'good';
            resultMessage = 'جيد! مع مراجعة بسيطة ستكون ممتازاً.';
            resultIcon = 'fas fa-thumbs-up';
        }

        quizContent.innerHTML = `
            <div class="quiz-results ${resultClass}">
                <div class="quiz-score">
                    <i class="${resultIcon}"></i>
                    <h3>النتيجة: ${this.score} من ${this.currentQuiz.questions.length}</h3>
                    <div class="quiz-percentage">${percentage}%</div>
                    <p class="quiz-message">${resultMessage}</p>
                </div>
                
                <div class="quiz-review">
                    <h4>مراجعة الإجابات:</h4>
                    ${this.currentQuiz.questions.map((question, index) => {
                        const userAnswer = this.userAnswers[index];
                        const isCorrect = userAnswer === question.correct;
                        
                        return `
                            <div class="quiz-review-item ${isCorrect ? 'correct' : 'incorrect'}">
                                <div class="quiz-review-question">
                                    <strong>السؤال ${index + 1}:</strong> ${question.question}
                                </div>
                                <div class="quiz-review-answer">
                                    <span class="quiz-review-label">إجابتك:</span>
                                    ${userAnswer !== undefined ? question.options[userAnswer] : 'لم تجب'}
                                    ${isCorrect ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-danger"></i>'}
                                </div>
                                ${!isCorrect ? `
                                    <div class="quiz-review-correct">
                                        <span class="quiz-review-label">الإجابة الصحيحة:</span>
                                        ${question.options[question.correct]}
                                    </div>
                                ` : ''}
                                <div class="quiz-review-explanation">
                                    <strong>التفسير:</strong> ${question.explanation}
                                </div>
                            </div>
                        `;
                    }).join('')}
                </div>
            </div>
        `;

        // تحديث أزرار التنقل
        const navigation = document.querySelector('.quiz-navigation');
        navigation.innerHTML = `
            <button class="btn btn-secondary" onclick="location.reload()">
                <i class="fas fa-redo"></i>
                إعادة الاختبار
            </button>
            <button class="btn btn-primary quiz-close-btn">
                <i class="fas fa-check"></i>
                إغلاق
            </button>
        `;

        navigation.querySelector('.quiz-close-btn').addEventListener('click', () => this.closeQuiz());
    }

    closeQuiz() {
        const modal = document.querySelector('.quiz-modal');
        if (modal) {
            modal.remove();
        }
    }
}

// تهيئة نظام الاختبارات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    new QuizSystem();
});
