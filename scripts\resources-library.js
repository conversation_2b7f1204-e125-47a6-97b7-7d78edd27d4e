/**
 * مكتبة الموارد القابلة للتحميل
 * Downloadable Resources Library
 */

class ResourcesLibrary {
    constructor() {
        this.resources = {
            templates: {
                title: 'القوالب والنماذج',
                icon: 'fas fa-file-alt',
                color: '#2196F3',
                items: [
                    {
                        id: 'maintenance_checklist_template',
                        title: 'قالب قائمة فحص الصيانة الوقائية',
                        description: 'قالب شامل لقائمة فحص الصيانة الوقائية للأجهزة الطبية',
                        type: 'PDF',
                        size: '245 KB',
                        category: 'templates',
                        downloadCount: 0,
                        lastUpdated: '2024-01-15'
                    },
                    {
                        id: 'risk_assessment_form',
                        title: 'نموذج تقييم المخاطر',
                        description: 'نموذج منظم لتقييم وإدارة مخاطر الأجهزة الطبية وفقاً لمعيار ISO 14971',
                        type: 'Excel',
                        size: '156 KB',
                        category: 'templates',
                        downloadCount: 0,
                        lastUpdated: '2024-01-16'
                    },
                    {
                        id: 'incident_report_template',
                        title: 'قالب تقرير الحوادث',
                        description: 'قالب لتوثيق حوادث الأجهزة الطبية والإجراءات المتخذة',
                        type: 'Word',
                        size: '89 KB',
                        category: 'templates',
                        downloadCount: 0,
                        lastUpdated: '2024-01-17'
                    },
                    {
                        id: 'calibration_record',
                        title: 'سجل المعايرة',
                        description: 'نموذج لتسجيل عمليات معايرة الأجهزة الطبية',
                        type: 'Excel',
                        size: '134 KB',
                        category: 'templates',
                        downloadCount: 0,
                        lastUpdated: '2024-01-18'
                    }
                ]
            },
            guides: {
                title: 'الأدلة والمراجع',
                icon: 'fas fa-book',
                color: '#4CAF50',
                items: [
                    {
                        id: 'aami_standards_summary',
                        title: 'ملخص معايير AAMI الأساسية',
                        description: 'دليل مرجعي سريع لأهم معايير AAMI للمهندسين السريريين',
                        type: 'PDF',
                        size: '1.2 MB',
                        category: 'guides',
                        downloadCount: 0,
                        lastUpdated: '2024-01-19'
                    },
                    {
                        id: 'electrical_safety_guide',
                        title: 'دليل السلامة الكهربائية',
                        description: 'دليل شامل لاختبارات السلامة الكهربائية للأجهزة الطبية',
                        type: 'PDF',
                        size: '2.1 MB',
                        category: 'guides',
                        downloadCount: 0,
                        lastUpdated: '2024-01-20'
                    },
                    {
                        id: 'cybersecurity_checklist',
                        title: 'قائمة فحص الأمن السيبراني',
                        description: 'قائمة شاملة لتقييم الأمان السيبراني للأجهزة الطبية المتصلة',
                        type: 'PDF',
                        size: '567 KB',
                        category: 'guides',
                        downloadCount: 0,
                        lastUpdated: '2024-01-21'
                    }
                ]
            },
            tools: {
                title: 'الأدوات والحاسبات',
                icon: 'fas fa-tools',
                color: '#FF9800',
                items: [
                    {
                        id: 'risk_calculator_excel',
                        title: 'حاسبة المخاطر - Excel',
                        description: 'حاسبة تفاعلية لتقييم المخاطر مع مصفوفة المخاطر التلقائية',
                        type: 'Excel',
                        size: '298 KB',
                        category: 'tools',
                        downloadCount: 0,
                        lastUpdated: '2024-01-22'
                    },
                    {
                        id: 'maintenance_scheduler',
                        title: 'جدولة الصيانة الذكية',
                        description: 'أداة لجدولة وتتبع أعمال الصيانة الوقائية',
                        type: 'Excel',
                        size: '445 KB',
                        category: 'tools',
                        downloadCount: 0,
                        lastUpdated: '2024-01-23'
                    },
                    {
                        id: 'inventory_tracker',
                        title: 'متتبع المخزون',
                        description: 'نظام لتتبع قطع الغيار والمستهلكات للأجهزة الطبية',
                        type: 'Excel',
                        size: '367 KB',
                        category: 'tools',
                        downloadCount: 0,
                        lastUpdated: '2024-01-24'
                    }
                ]
            },
            presentations: {
                title: 'العروض التقديمية',
                icon: 'fas fa-presentation-screen',
                color: '#9C27B0',
                items: [
                    {
                        id: 'aami_introduction_ppt',
                        title: 'مقدمة في معايير AAMI',
                        description: 'عرض تقديمي شامل لتعريف معايير AAMI وأهميتها',
                        type: 'PowerPoint',
                        size: '3.4 MB',
                        category: 'presentations',
                        downloadCount: 0,
                        lastUpdated: '2024-01-25'
                    },
                    {
                        id: 'risk_management_presentation',
                        title: 'إدارة المخاطر في الأجهزة الطبية',
                        description: 'عرض تفصيلي لمنهجية إدارة المخاطر وفقاً لمعيار ISO 14971',
                        type: 'PowerPoint',
                        size: '2.8 MB',
                        category: 'presentations',
                        downloadCount: 0,
                        lastUpdated: '2024-01-26'
                    }
                ]
            }
        };
        
        this.downloadHistory = this.loadDownloadHistory();
        this.init();
    }

    init() {
        this.createResourcesInterface();
        this.setupEventListeners();
    }

    loadDownloadHistory() {
        try {
            return JSON.parse(localStorage.getItem('aami_download_history') || '[]');
        } catch {
            return [];
        }
    }

    saveDownloadHistory() {
        try {
            localStorage.setItem('aami_download_history', JSON.stringify(this.downloadHistory));
        } catch (error) {
            console.warn('خطأ في حفظ تاريخ التحميل:', error);
        }
    }

    createResourcesInterface() {
        // إنشاء زر مكتبة الموارد
        const resourcesButton = document.createElement('button');
        resourcesButton.className = 'resources-btn';
        resourcesButton.innerHTML = `
            <i class="fas fa-download"></i>
            <span>مكتبة الموارد</span>
            <div class="resources-indicator">
                <span class="new-badge">جديد</span>
            </div>
        `;
        resourcesButton.title = 'تحميل القوالب والأدوات والمراجع';
        
        document.body.appendChild(resourcesButton);
        
        // إنشاء نافذة مكتبة الموارد
        this.createResourcesModal();
    }

    createResourcesModal() {
        const modal = document.createElement('div');
        modal.className = 'resources-modal';
        modal.innerHTML = `
            <div class="resources-modal-content">
                <div class="resources-header">
                    <h3>
                        <i class="fas fa-download"></i>
                        مكتبة الموارد القابلة للتحميل
                    </h3>
                    <button class="resources-close-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="resources-tabs">
                    <button class="resource-tab-btn active" data-tab="all">جميع الموارد</button>
                    <button class="resource-tab-btn" data-tab="templates">القوالب</button>
                    <button class="resource-tab-btn" data-tab="guides">الأدلة</button>
                    <button class="resource-tab-btn" data-tab="tools">الأدوات</button>
                    <button class="resource-tab-btn" data-tab="presentations">العروض</button>
                    <button class="resource-tab-btn" data-tab="history">التحميلات</button>
                </div>
                
                <div class="resources-content">
                    <div class="resources-search">
                        <i class="fas fa-search"></i>
                        <input type="text" id="resources-search" placeholder="ابحث في الموارد...">
                    </div>
                    
                    <div class="resources-grid" id="resources-grid">
                        ${this.renderAllResources()}
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
    }

    renderAllResources() {
        let html = '';
        
        Object.entries(this.resources).forEach(([categoryId, category]) => {
            html += `
                <div class="resource-category" data-category="${categoryId}">
                    <div class="category-header">
                        <div class="category-icon" style="background: ${category.color}">
                            <i class="${category.icon}"></i>
                        </div>
                        <h4>${category.title}</h4>
                        <span class="category-count">${category.items.length} عنصر</span>
                    </div>
                    
                    <div class="category-resources">
                        ${category.items.map(item => this.renderResourceItem(item, category.color)).join('')}
                    </div>
                </div>
            `;
        });
        
        return html;
    }

    renderResourceItem(item, categoryColor) {
        const fileIcon = this.getFileIcon(item.type);
        const downloadCount = this.getDownloadCount(item.id);
        const lastUpdated = new Date(item.lastUpdated).toLocaleDateString('ar-SA');
        
        return `
            <div class="resource-item" data-resource-id="${item.id}">
                <div class="resource-header">
                    <div class="file-icon" style="background: ${categoryColor}">
                        <i class="${fileIcon}"></i>
                    </div>
                    <div class="file-type-badge">${item.type}</div>
                </div>
                
                <div class="resource-content">
                    <h5 class="resource-title">${item.title}</h5>
                    <p class="resource-description">${item.description}</p>
                    
                    <div class="resource-meta">
                        <span class="resource-size">
                            <i class="fas fa-file"></i>
                            ${item.size}
                        </span>
                        <span class="resource-downloads">
                            <i class="fas fa-download"></i>
                            ${downloadCount} تحميل
                        </span>
                        <span class="resource-date">
                            <i class="fas fa-calendar"></i>
                            ${lastUpdated}
                        </span>
                    </div>
                </div>
                
                <div class="resource-actions">
                    <button class="btn btn-primary download-resource-btn" data-resource-id="${item.id}">
                        <i class="fas fa-download"></i>
                        تحميل
                    </button>
                    <button class="btn btn-secondary preview-resource-btn" data-resource-id="${item.id}">
                        <i class="fas fa-eye"></i>
                        معاينة
                    </button>
                </div>
            </div>
        `;
    }

    renderDownloadHistory() {
        if (this.downloadHistory.length === 0) {
            return `
                <div class="no-downloads">
                    <i class="fas fa-download"></i>
                    <h4>لا توجد تحميلات سابقة</h4>
                    <p>ابدأ بتحميل الموارد المفيدة لعملك</p>
                </div>
            `;
        }
        
        return `
            <div class="download-history-list">
                ${this.downloadHistory.map(download => `
                    <div class="download-history-item">
                        <div class="download-icon">
                            <i class="${this.getFileIcon(download.type)}"></i>
                        </div>
                        <div class="download-content">
                            <h5>${download.title}</h5>
                            <div class="download-meta">
                                <span class="download-date">${new Date(download.downloadDate).toLocaleDateString('ar-SA')}</span>
                                <span class="download-type">${download.type}</span>
                                <span class="download-size">${download.size}</span>
                            </div>
                        </div>
                        <div class="download-actions">
                            <button class="btn btn-sm btn-secondary redownload-btn" data-resource-id="${download.id}">
                                <i class="fas fa-redo"></i>
                                إعادة تحميل
                            </button>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    setupEventListeners() {
        // فتح/إغلاق نافذة الموارد
        document.addEventListener('click', (e) => {
            if (e.target.closest('.resources-btn')) {
                this.toggleResourcesModal();
            }
            
            if (e.target.closest('.resources-close-btn')) {
                this.closeResourcesModal();
            }
            
            // تبديل التبويبات
            if (e.target.classList.contains('resource-tab-btn')) {
                this.switchResourceTab(e.target.dataset.tab);
            }
            
            // تحميل مورد
            if (e.target.closest('.download-resource-btn')) {
                const resourceId = e.target.closest('.download-resource-btn').dataset.resourceId;
                this.downloadResource(resourceId);
            }
            
            // معاينة مورد
            if (e.target.closest('.preview-resource-btn')) {
                const resourceId = e.target.closest('.preview-resource-btn').dataset.resourceId;
                this.previewResource(resourceId);
            }
            
            // إعادة تحميل
            if (e.target.closest('.redownload-btn')) {
                const resourceId = e.target.closest('.redownload-btn').dataset.resourceId;
                this.downloadResource(resourceId);
            }
        });

        // البحث في الموارد
        document.addEventListener('input', (e) => {
            if (e.target.id === 'resources-search') {
                this.filterResources(e.target.value);
            }
        });

        // إغلاق النافذة عند النقر خارجها
        document.addEventListener('click', (e) => {
            const modal = document.querySelector('.resources-modal');
            if (e.target === modal) {
                this.closeResourcesModal();
            }
        });
    }

    downloadResource(resourceId) {
        const resource = this.findResourceById(resourceId);
        if (!resource) return;
        
        // محاكاة تحميل الملف
        this.simulateDownload(resource);
        
        // إضافة إلى تاريخ التحميل
        this.addToDownloadHistory(resource);
        
        // تحديث عداد التحميل
        this.incrementDownloadCount(resourceId);
        
        // إظهار رسالة نجاح
        this.showNotification(`تم تحميل ${resource.title} بنجاح`, 'success');
    }

    simulateDownload(resource) {
        // إنشاء محتوى الملف بناءً على النوع
        let content = '';
        let mimeType = '';
        let filename = '';
        
        switch (resource.type) {
            case 'PDF':
                content = this.generatePDFContent(resource);
                mimeType = 'application/pdf';
                filename = `${resource.title.replace(/\s+/g, '_')}.pdf`;
                break;
            case 'Excel':
                content = this.generateExcelContent(resource);
                mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                filename = `${resource.title.replace(/\s+/g, '_')}.xlsx`;
                break;
            case 'Word':
                content = this.generateWordContent(resource);
                mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
                filename = `${resource.title.replace(/\s+/g, '_')}.docx`;
                break;
            case 'PowerPoint':
                content = this.generatePowerPointContent(resource);
                mimeType = 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
                filename = `${resource.title.replace(/\s+/g, '_')}.pptx`;
                break;
            default:
                content = this.generateTextContent(resource);
                mimeType = 'text/plain';
                filename = `${resource.title.replace(/\s+/g, '_')}.txt`;
        }
        
        // تحميل الملف
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    generatePDFContent(resource) {
        // محاكاة محتوى PDF بسيط
        return `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
72 720 Td
(${resource.title}) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
300
%%EOF`;
    }

    generateExcelContent(resource) {
        // محاكاة محتوى Excel بصيغة CSV
        return `العنوان,الوصف,النوع,التاريخ
${resource.title},"${resource.description}",${resource.type},${resource.lastUpdated}

هذا ملف تجريبي من مكتبة موارد دليل معايير AAMI
يحتوي على البيانات الأساسية للمورد المطلوب

للحصول على النسخة الكاملة، يرجى زيارة الموقع الرسمي`;
    }

    generateWordContent(resource) {
        // محاكاة محتوى Word بصيغة نصية
        return `${resource.title}

${resource.description}

هذا مستند تجريبي من مكتبة موارد دليل معايير AAMI للمهندسين السريريين.

المحتوى:
- معلومات أساسية عن ${resource.title}
- إرشادات التطبيق
- أمثلة عملية
- مراجع ومصادر إضافية

تاريخ آخر تحديث: ${resource.lastUpdated}
النوع: ${resource.type}
الحجم: ${resource.size}

للحصول على النسخة الكاملة والمحدثة، يرجى زيارة الموقع الرسمي لدليل معايير AAMI.`;
    }

    generatePowerPointContent(resource) {
        // محاكاة محتوى PowerPoint بصيغة نصية
        return `عرض تقديمي: ${resource.title}

الشريحة 1: العنوان
${resource.title}
دليل معايير AAMI للمهندسين السريريين

الشريحة 2: المقدمة
${resource.description}

الشريحة 3: الأهداف
- فهم المفاهيم الأساسية
- تطبيق المعايير عملياً
- تحسين الأداء المهني

الشريحة 4: المحتوى الرئيسي
[محتوى تفصيلي حسب نوع المورد]

الشريحة 5: الخلاصة
- النقاط الرئيسية
- التوصيات
- الخطوات التالية

هذا عرض تجريبي من مكتبة موارد دليل معايير AAMI.
للحصول على النسخة الكاملة، يرجى زيارة الموقع الرسمي.`;
    }

    generateTextContent(resource) {
        return `${resource.title}

الوصف: ${resource.description}
النوع: ${resource.type}
الحجم: ${resource.size}
آخر تحديث: ${resource.lastUpdated}

هذا ملف تجريبي من مكتبة موارد دليل معايير AAMI للمهندسين السريريين.

المحتوى يشمل:
- معلومات تفصيلية عن الموضوع
- إرشادات التطبيق العملي
- أمثلة وحالات دراسية
- مراجع ومصادر إضافية

للحصول على النسخة الكاملة والمحدثة من هذا المورد، يرجى زيارة الموقع الرسمي لدليل معايير AAMI.`;
    }

    previewResource(resourceId) {
        const resource = this.findResourceById(resourceId);
        if (!resource) return;
        
        // إنشاء نافذة معاينة
        const previewModal = document.createElement('div');
        previewModal.className = 'resource-preview-modal';
        previewModal.innerHTML = `
            <div class="preview-modal-content">
                <div class="preview-header">
                    <h3>معاينة: ${resource.title}</h3>
                    <button class="preview-close-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="preview-content">
                    <div class="resource-info">
                        <div class="info-item">
                            <strong>النوع:</strong> ${resource.type}
                        </div>
                        <div class="info-item">
                            <strong>الحجم:</strong> ${resource.size}
                        </div>
                        <div class="info-item">
                            <strong>آخر تحديث:</strong> ${new Date(resource.lastUpdated).toLocaleDateString('ar-SA')}
                        </div>
                    </div>
                    
                    <div class="resource-description">
                        <h4>الوصف:</h4>
                        <p>${resource.description}</p>
                    </div>
                    
                    <div class="preview-placeholder">
                        <i class="${this.getFileIcon(resource.type)}"></i>
                        <h4>معاينة ${resource.type}</h4>
                        <p>هذا المورد يحتوي على محتوى تفصيلي حول ${resource.title}</p>
                        <p>لعرض المحتوى الكامل، يرجى تحميل الملف</p>
                    </div>
                </div>
                
                <div class="preview-actions">
                    <button class="btn btn-primary download-from-preview-btn" data-resource-id="${resourceId}">
                        <i class="fas fa-download"></i>
                        تحميل الآن
                    </button>
                    <button class="btn btn-secondary close-preview-btn">
                        <i class="fas fa-times"></i>
                        إغلاق
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(previewModal);
        
        // إضافة مستمعي الأحداث
        previewModal.querySelector('.preview-close-btn').addEventListener('click', () => {
            previewModal.remove();
        });
        
        previewModal.querySelector('.close-preview-btn').addEventListener('click', () => {
            previewModal.remove();
        });
        
        previewModal.querySelector('.download-from-preview-btn').addEventListener('click', () => {
            this.downloadResource(resourceId);
            previewModal.remove();
        });
        
        previewModal.addEventListener('click', (e) => {
            if (e.target === previewModal) {
                previewModal.remove();
            }
        });
        
        setTimeout(() => previewModal.classList.add('active'), 10);
    }

    findResourceById(resourceId) {
        for (const category of Object.values(this.resources)) {
            const resource = category.items.find(item => item.id === resourceId);
            if (resource) return resource;
        }
        return null;
    }

    addToDownloadHistory(resource) {
        const downloadRecord = {
            id: resource.id,
            title: resource.title,
            type: resource.type,
            size: resource.size,
            downloadDate: new Date().toISOString()
        };
        
        // إزالة التحميل السابق إذا كان موجوداً
        this.downloadHistory = this.downloadHistory.filter(item => item.id !== resource.id);
        
        // إضافة في المقدمة
        this.downloadHistory.unshift(downloadRecord);
        
        // الاحتفاظ بآخر 50 تحميل
        this.downloadHistory = this.downloadHistory.slice(0, 50);
        
        this.saveDownloadHistory();
    }

    incrementDownloadCount(resourceId) {
        const downloadCounts = JSON.parse(localStorage.getItem('aami_download_counts') || '{}');
        downloadCounts[resourceId] = (downloadCounts[resourceId] || 0) + 1;
        localStorage.setItem('aami_download_counts', JSON.stringify(downloadCounts));
    }

    getDownloadCount(resourceId) {
        const downloadCounts = JSON.parse(localStorage.getItem('aami_download_counts') || '{}');
        return downloadCounts[resourceId] || 0;
    }

    getFileIcon(fileType) {
        const icons = {
            'PDF': 'fas fa-file-pdf',
            'Excel': 'fas fa-file-excel',
            'Word': 'fas fa-file-word',
            'PowerPoint': 'fas fa-file-powerpoint',
            'Text': 'fas fa-file-alt'
        };
        return icons[fileType] || 'fas fa-file';
    }

    switchResourceTab(tabName) {
        // إزالة الفئة النشطة من جميع التبويبات
        document.querySelectorAll('.resource-tab-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        
        // تحديث المحتوى
        const resourcesGrid = document.getElementById('resources-grid');
        
        if (tabName === 'all') {
            resourcesGrid.innerHTML = this.renderAllResources();
        } else if (tabName === 'history') {
            resourcesGrid.innerHTML = this.renderDownloadHistory();
        } else {
            resourcesGrid.innerHTML = this.renderCategoryResources(tabName);
        }
    }

    renderCategoryResources(categoryId) {
        const category = this.resources[categoryId];
        if (!category) return '';
        
        return `
            <div class="resource-category" data-category="${categoryId}">
                <div class="category-header">
                    <div class="category-icon" style="background: ${category.color}">
                        <i class="${category.icon}"></i>
                    </div>
                    <h4>${category.title}</h4>
                    <span class="category-count">${category.items.length} عنصر</span>
                </div>
                
                <div class="category-resources">
                    ${category.items.map(item => this.renderResourceItem(item, category.color)).join('')}
                </div>
            </div>
        `;
    }

    filterResources(query) {
        if (!query.trim()) {
            this.switchResourceTab('all');
            return;
        }
        
        const filteredResources = [];
        
        Object.entries(this.resources).forEach(([categoryId, category]) => {
            const matchingItems = category.items.filter(item =>
                item.title.toLowerCase().includes(query.toLowerCase()) ||
                item.description.toLowerCase().includes(query.toLowerCase()) ||
                item.type.toLowerCase().includes(query.toLowerCase())
            );
            
            if (matchingItems.length > 0) {
                filteredResources.push({
                    ...category,
                    items: matchingItems,
                    categoryId
                });
            }
        });
        
        const resourcesGrid = document.getElementById('resources-grid');
        if (filteredResources.length === 0) {
            resourcesGrid.innerHTML = `
                <div class="no-results">
                    <i class="fas fa-search"></i>
                    <h4>لم يتم العثور على موارد</h4>
                    <p>جرب استخدام كلمات مختلفة أو تحقق من الإملاء</p>
                </div>
            `;
        } else {
            resourcesGrid.innerHTML = filteredResources.map(category => `
                <div class="resource-category" data-category="${category.categoryId}">
                    <div class="category-header">
                        <div class="category-icon" style="background: ${category.color}">
                            <i class="${category.icon}"></i>
                        </div>
                        <h4>${category.title}</h4>
                        <span class="category-count">${category.items.length} عنصر</span>
                    </div>
                    
                    <div class="category-resources">
                        ${category.items.map(item => this.renderResourceItem(item, category.color)).join('')}
                    </div>
                </div>
            `).join('');
        }
    }

    toggleResourcesModal() {
        const modal = document.querySelector('.resources-modal');
        if (modal.style.display === 'flex') {
            this.closeResourcesModal();
        } else {
            this.openResourcesModal();
        }
    }

    openResourcesModal() {
        const modal = document.querySelector('.resources-modal');
        modal.style.display = 'flex';
        setTimeout(() => modal.classList.add('active'), 10);
        
        // التركيز على حقل البحث
        const searchInput = document.getElementById('resources-search');
        if (searchInput) {
            searchInput.focus();
        }
    }

    closeResourcesModal() {
        const modal = document.querySelector('.resources-modal');
        modal.classList.remove('active');
        setTimeout(() => modal.style.display = 'none', 300);
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `resource-notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-info-circle'}"></i>
                <span>${message}</span>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => notification.classList.add('show'), 100);
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    // طرق عامة للاستخدام من الخارج
    getTotalResourcesCount() {
        return Object.values(this.resources).reduce((total, category) => total + category.items.length, 0);
    }

    getResourcesByType(type) {
        const resources = [];
        Object.values(this.resources).forEach(category => {
            resources.push(...category.items.filter(item => item.type === type));
        });
        return resources;
    }

    getMostDownloadedResources(limit = 5) {
        const downloadCounts = JSON.parse(localStorage.getItem('aami_download_counts') || '{}');
        const allResources = [];
        
        Object.values(this.resources).forEach(category => {
            allResources.push(...category.items);
        });
        
        return allResources
            .map(resource => ({
                ...resource,
                downloadCount: downloadCounts[resource.id] || 0
            }))
            .sort((a, b) => b.downloadCount - a.downloadCount)
            .slice(0, limit);
    }
}

// تهيئة مكتبة الموارد عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.resourcesLibrary = new ResourcesLibrary();
});
