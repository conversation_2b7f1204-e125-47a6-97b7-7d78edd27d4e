# الدليل العملي لمعايير AAMI في الهندسة السريرية

## نظرة عامة

هذا الموقع الإلكتروني هو تمثيل رقمي شامل للدليل العملي لمعايير AAMI في الهندسة السريرية، والذي ألفه د. محمد يعقوب إسماعيل من جامعة السودان للعلوم والتكنولوجيا.

## هيكل الموقع

### الصفحات الرئيسية
- `index.html` - الصفحة الرئيسية
- `pages/introduction.html` - المقدمة
- `pages/table-of-contents.html` - فهرس المحتويات
- `pages/references.html` - المراجع والمصادر
- `pages/appendices.html` - الملاحق

### أجزاء الدليل

#### الجزء الأول: الأسس والمبادئ
- `pages/part1/index.html` - صفحة الجزء الأول
- `pages/part1/chapter1.html` - الفصل الأول: مقدمة في عالم المعايير الطبية
- `pages/part1/chapter2.html` - الفصل الثاني: فلسفة المعايير (قيد الإنشاء)
- `pages/part1/chapter3.html` - الفصل الثالث: بنية المصداقية (قيد الإنشاء)

#### الجزء الثاني: المعايير في الممارسة
- `pages/part2/index.html` - صفحة الجزء الثاني (قيد الإنشاء)
- `pages/part2/chapter4.html` - الفصل الرابع: خريطة عالم المعايير (قيد الإنشاء)
- `pages/part2/chapter5.html` - الفصل الخامس: التطبيق العملي (قيد الإنشاء)

#### الجزء الثالث: آفاق المستقبل
- `pages/part3/index.html` - صفحة الجزء الثالث (قيد الإنشاء)
- `pages/part3/chapter6.html` - الفصل السادس: جبهات المستقبل (قيد الإنشاء)
- `pages/part3/chapter7.html` - الفصل السابع: صندوق أدوات المهندس السريري (قيد الإنشاء)

### الملفات التقنية

#### CSS
- `styles/main.css` - الأنماط الرئيسية للموقع
- `styles/pages.css` - أنماط خاصة بالصفحات الداخلية

#### JavaScript
- `scripts/main.js` - الوظائف التفاعلية الرئيسية

## الميزات التقنية

### التصميم المتجاوب
- يدعم جميع أحجام الشاشات (سطح المكتب، الأجهزة اللوحية، الهواتف الذكية)
- تصميم متكيف مع اتجاه النص العربي (RTL)

### الوظائف التفاعلية
- قائمة تنقل ديناميكية
- وظيفة البحث في المحتوى
- تأثيرات التمرير والحركة
- تنقل سلس بين الأقسام

### إمكانية الوصول
- دعم قارئات الشاشة
- تنقل بلوحة المفاتيح
- تباين ألوان مناسب
- خطوط واضحة ومقروءة

### الخطوط والأيقونات
- خط Noto Sans Arabic للنصوص العربية
- مكتبة Font Awesome للأيقونات

## كيفية الاستخدام

1. افتح `index.html` في متصفح الويب
2. استخدم قائمة التنقل للوصول إلى الأقسام المختلفة
3. استخدم فهرس المحتويات للتنقل المباشر إلى فصول محددة
4. استخدم وظيفة البحث (Ctrl+K) للعثور على محتوى محدد

## المتطلبات التقنية

- متصفح ويب حديث يدعم HTML5 و CSS3
- اتصال بالإنترنت لتحميل الخطوط والأيقونات من CDN
- دعم JavaScript للوظائف التفاعلية

## المؤلف

**د. محمد يعقوب إسماعيل**
- قسم الهندسة الطبية الحيوية
- جامعة السودان للعلوم والتكنولوجيا (SUST)
- © 2025

## حقوق النشر

جميع الحقوق محفوظة © 2025. هذا العمل محمي بحقوق الطبع والنشر.

## ملاحظات التطوير

### المكتمل
- ✅ الصفحة الرئيسية
- ✅ نظام التنقل
- ✅ التصميم المتجاوب
- ✅ المقدمة
- ✅ فهرس المحتويات
- ✅ المراجع والملاحق
- ✅ الفصل الأول من الجزء الأول

### قيد التطوير
- 🔄 باقي فصول الجزء الأول
- 🔄 الجزء الثاني كاملاً
- 🔄 الجزء الثالث كاملاً
- 🔄 وظائف البحث المتقدمة
- 🔄 إضافة المزيد من المحتوى التفاعلي

### التحسينات المستقبلية
- 📋 إضافة وضع القراءة الليلية
- 📋 تحسين أداء التحميل
- 📋 إضافة وظائف الطباعة
- 📋 دعم تصدير المحتوى كـ PDF
- 📋 إضافة تعليقات وملاحظات تفاعلية

## الدعم التقني

للمساعدة التقنية أو الاستفسارات حول المحتوى، يرجى التواصل مع المؤلف أو فريق التطوير.
